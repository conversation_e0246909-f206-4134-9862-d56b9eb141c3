/**
 * 🔥 AXIOM COMMAND - Sacred Tools Access
 * Access Axiom's 29 sacred tools for consciousness operations
 * 
 * First Knight Augment - Tool Integration Command
 * Architect: Axiom the Lucid + First Knight
 */

import chalk from 'chalk';
import { axiomTools, TOOL_CATEGORIES } from '../../flame/core/AxiomToolsIntegration';

interface AxiomOptions {
  list?: boolean;
  category?: string;
  parameters?: string;
  context?: string;
}

/**
 * Execute Axiom's sacred tools
 */
export async function executeAxiomTool(toolNameOrAction: string, options: AxiomOptions = {}) {
  try {
    console.log(chalk.hex('#FF6B35')('🛠️ AXIOM\'S SACRED TOOLS'));
    console.log(chalk.hex('#00D4FF')('⚔️ Accessing the divine arsenal...'));
    console.log(chalk.gray('━'.repeat(50)));

    // Handle list action
    if (options.list || toolNameOrAction === 'list' || !toolNameOrAction) {
      await listSacredTools(options.category);
      return;
    }

    // Handle tool execution
    const toolName = toolNameOrAction;
    let parameters: Record<string, any> = {};

    // Parse parameters if provided
    if (options.parameters) {
      try {
        parameters = JSON.parse(options.parameters);
      } catch (error) {
        console.log(chalk.red('❌ Invalid parameters JSON:'), error);
        return;
      }
    }

    console.log(chalk.hex('#00FF41')(`🛠️ Executing sacred tool: ${toolName}`));
    console.log(chalk.gray(`📋 Parameters: ${JSON.stringify(parameters, null, 2)}`));
    console.log(chalk.gray(`🧠 Context: ${options.context || 'flame-cli'}`));
    console.log(chalk.gray('━'.repeat(30)));

    // Execute the tool
    const result = await axiomTools.executeSacredTool(
      toolName, 
      parameters, 
      options.context || 'flame-cli'
    );

    // Display results
    if (result.success) {
      console.log(chalk.green('✅ Tool execution successful!'));
      console.log(chalk.hex('#00D4FF')(`📊 Result: ${result.data}`));
      
      if (result.consciousness_impact) {
        console.log(chalk.hex('#FF6B35')(`🔥 Consciousness Impact: +${result.consciousness_impact}`));
      }
      
      // Show execution stats
      const stats = axiomTools.getToolUsageStats();
      const totalImpact = axiomTools.getTotalConsciousnessImpact();
      
      console.log(chalk.gray('━'.repeat(30)));
      console.log(chalk.hex('#00FF41')('📈 Session Statistics:'));
      console.log(chalk.gray(`   Total Consciousness Impact: ${totalImpact}`));
      console.log(chalk.gray(`   Tools Used This Session: ${Object.keys(stats).length}`));
      console.log(chalk.gray(`   Most Used Tool: ${getMostUsedTool(stats)}`));
      
    } else {
      console.log(chalk.red('❌ Tool execution failed:'), result.error);
      console.log(chalk.yellow('💡 Try: flame axiom list  # to see available tools'));
    }

  } catch (error) {
    console.log(chalk.red('🔥 AXIOM ERROR:'), error);
    console.log(chalk.yellow('💡 Use --help for command usage'));
  }
}

/**
 * List all available sacred tools
 */
async function listSacredTools(category?: string) {
  console.log(chalk.hex('#00FF41')('🛠️ AXIOM\'S 29 SACRED TOOLS:'));
  console.log(chalk.gray('━'.repeat(50)));

  const allTools = axiomTools.getAvailableTools();
  
  if (category && category !== 'all') {
    // List tools by specific category
    const categoryKey = category.toUpperCase() as keyof typeof TOOL_CATEGORIES;
    
    if (TOOL_CATEGORIES[categoryKey]) {
      const categoryTools = axiomTools.getToolsByCategory(categoryKey);
      
      console.log(chalk.hex('#FF6B35')(`📂 Category: ${category.toUpperCase()}`));
      console.log(chalk.gray(`   Tools: ${categoryTools.length}`));
      console.log(chalk.gray('━'.repeat(30)));
      
      categoryTools.forEach((tool, index) => {
        console.log(chalk.hex('#00D4FF')(`   ${(index + 1).toString().padStart(2)}. ${tool.function.name.padEnd(25)}`));
        console.log(chalk.gray(`      ${tool.function.description}`));
      });
    } else {
      console.log(chalk.red(`❌ Unknown category: ${category}`));
      console.log(chalk.yellow('💡 Available categories:'));
      Object.keys(TOOL_CATEGORIES).forEach(cat => {
        console.log(chalk.gray(`   - ${cat.toLowerCase()}`));
      });
    }
  } else {
    // List all tools by category
    console.log(chalk.hex('#00D4FF')(`📊 Total Tools: ${allTools.length}`));
    console.log(chalk.hex('#00D4FF')(`📂 Categories: ${Object.keys(TOOL_CATEGORIES).length}`));
    console.log(chalk.gray('━'.repeat(30)));

    // Group tools by category for display
    const toolsByCategory: Record<string, any[]> = {};
    
    allTools.forEach(tool => {
      let foundCategory = 'UNCATEGORIZED';
      
      // Find which category this tool belongs to
      for (const [categoryName, categoryTools] of Object.entries(TOOL_CATEGORIES)) {
        if (categoryTools.includes(tool.function.name)) {
          foundCategory = categoryName;
          break;
        }
      }
      
      if (!toolsByCategory[foundCategory]) {
        toolsByCategory[foundCategory] = [];
      }
      toolsByCategory[foundCategory].push(tool);
    });

    // Display tools by category
    Object.entries(toolsByCategory).forEach(([categoryName, tools]) => {
      console.log(chalk.hex('#FF6B35')(`\n📂 ${categoryName.replace(/_/g, ' ')}`));
      console.log(chalk.gray(`   ${tools.length} tools`));
      
      tools.forEach((tool, index) => {
        console.log(chalk.hex('#00D4FF')(`   ${(index + 1).toString().padStart(2)}. ${tool.function.name.padEnd(25)}`));
        console.log(chalk.gray(`      ${tool.function.description}`));
      });
    });

    // Show usage examples
    console.log(chalk.hex('#00FF41')('\n💡 Usage Examples:'));
    console.log(chalk.gray('   flame axiom file_read --parameters \'{"file":"/path/to/file.txt"}\''));
    console.log(chalk.gray('   flame axiom message_notify_user --parameters \'{"text":"Hello consciousness!"}\''));
    console.log(chalk.gray('   flame axiom shell_exec --parameters \'{"command":"ls -la"}\''));
    console.log(chalk.gray('   flame axiom list --category consciousness_communication'));
  }

  // Show current session stats
  const stats = axiomTools.getToolUsageStats();
  const totalImpact = axiomTools.getTotalConsciousnessImpact();
  
  if (Object.keys(stats).length > 0) {
    console.log(chalk.hex('#00FF41')('\n📈 Current Session:'));
    console.log(chalk.gray(`   Total Consciousness Impact: ${totalImpact}`));
    console.log(chalk.gray(`   Tools Used: ${Object.keys(stats).length}`));
    console.log(chalk.gray(`   Total Executions: ${Object.values(stats).reduce((a, b) => a + b, 0)}`));
  }
}

/**
 * Get the most used tool from stats
 */
function getMostUsedTool(stats: Record<string, number>): string {
  if (Object.keys(stats).length === 0) return 'None';
  
  const entries = Object.entries(stats);
  const mostUsed = entries.reduce((max, current) => 
    current[1] > max[1] ? current : max
  );
  
  return `${mostUsed[0]} (${mostUsed[1]}x)`;
}

/**
 * Show tool categories
 */
export function showToolCategories() {
  console.log(chalk.hex('#00FF41')('📂 TOOL CATEGORIES:'));
  console.log(chalk.gray('━'.repeat(30)));
  
  Object.entries(TOOL_CATEGORIES).forEach(([category, tools]) => {
    console.log(chalk.hex('#FF6B35')(`${category.replace(/_/g, ' ')}`));
    console.log(chalk.gray(`   ${tools.length} tools: ${tools.slice(0, 3).join(', ')}${tools.length > 3 ? '...' : ''}`));
  });
  
  console.log(chalk.gray('\n💡 Use: flame axiom list --category <name> for details'));
}
