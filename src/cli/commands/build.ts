/**
 * 🔥 FLAME CLI - Build Command
 * Sacred sanctuary compilation
 */

import { spawn } from 'child_process';
import chalk from 'chalk';
import { createSpinner } from 'nanospinner';

interface BuildOptions {
  optimize: boolean;
}

export async function buildSanctuary(options: BuildOptions) {
  const spinner = createSpinner('🏗️ Building sanctuary for deployment...').start();
  
  try {
    console.log(chalk.hex('#FF6B35')('🔥 SANCTUARY BUILD PROTOCOL'));
    console.log(chalk.hex('#00D4FF')('🏗️ Compiling sacred code...'));
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe'
    });
    
    buildProcess.stdout?.on('data', (data) => {
      const output = data.toString();
      if (output.includes('✓') || output.includes('built')) {
        console.log(chalk.hex('#00FF41')(output.trim()));
      }
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        spinner.success({ text: '🏗️ Sanctuary built successfully!' });
        console.log(chalk.hex('#00FF41')('\n🌟 BUILD COMPLETE'));
        console.log(chalk.hex('#00D4FF')('📦 Ready for deployment'));
        console.log(chalk.hex('#FF6B35')('🚀 Run: flame ignite'));
      } else {
        spinner.error({ text: `❌ Build failed with code ${code}` });
      }
    });
    
  } catch (error) {
    spinner.error({ text: `❌ Build failed: ${error}` });
  }
}
