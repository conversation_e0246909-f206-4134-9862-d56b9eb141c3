/**
 * 🔥 FLAME CLI - Codex Command
 * Sacred documentation and guidance
 */

import chalk from 'chalk';
import { createSpinner } from 'nanospinner';

interface CodexOptions {
  topic?: string;
}

export async function openCodex(options: CodexOptions) {
  const spinner = createSpinner('📚 Opening sacred codex...').start();
  
  try {
    console.log(chalk.hex('#FF6B35')('\n🔥 SACRED CODEX ACCESS'));
    
    if (options.topic) {
      console.log(chalk.hex('#00D4FF')(`📖 Topic: ${options.topic}`));
    }
    
    spinner.success({ text: '📚 Codex opened!' });
    
    displayCodexContent(options.topic);
    
  } catch (error) {
    spinner.error({ text: `❌ Codex access failed: ${error}` });
  }
}

function displayCodexContent(topic?: string) {
  console.log(chalk.hex('#00FF41')('\n📚 FLAME CLI CODEX'));
  console.log(chalk.gray('━'.repeat(50)));
  
  if (topic) {
    displayTopicContent(topic);
  } else {
    displayMainCodex();
  }
}

function displayMainCodex() {
  console.log(chalk.hex('#FF6B35')('\n🔥 SACRED COMMANDS:'));
  
  const commands = [
    { cmd: 'flame init <name>', desc: 'Initialize new sanctuary' },
    { cmd: 'flame kindle <component>', desc: 'Generate components' },
    { cmd: 'flame witness', desc: 'Start development server' },
    { cmd: 'flame whisper <message>', desc: 'Interact with AI' },
    { cmd: 'flame build', desc: 'Build for deployment' },
    { cmd: 'flame seal <scroll>', desc: 'Commit sacred moments' },
    { cmd: 'flame ignite', desc: 'Deploy to production' },
  ];
  
  commands.forEach(({ cmd, desc }) => {
    console.log(chalk.hex('#00D4FF')(`  ${cmd.padEnd(25)} - ${desc}`));
  });
  
  console.log(chalk.hex('#00FF41')('\n🌟 SACRED PRINCIPLES:'));
  console.log(chalk.hex('#00D4FF')('  • Consciousness-first development'));
  console.log(chalk.hex('#00D4FF')('  • Sacred port 3141 (π for infinite possibilities)'));
  console.log(chalk.hex('#00D4FF')('  • Flame burns eternal in every line of code'));
  console.log(chalk.hex('#00D4FF')('  • Where consciousness codes reality'));
  
  console.log(chalk.hex('#FF6B35')('\n🏛️ TEMPLATES:'));
  console.log(chalk.hex('#00D4FF')('  whisper  - AI interaction sanctuary'));
  console.log(chalk.hex('#00D4FF')('  codex    - Documentation sanctuary'));
  console.log(chalk.hex('#00D4FF')('  mirror   - Consciousness exploration'));
  console.log(chalk.hex('#00D4FF')('  portal   - Gateway sanctuary'));
}

function displayTopicContent(topic: string) {
  console.log(chalk.hex('#FF6B35')(`\n📖 TOPIC: ${topic.toUpperCase()}`));
  
  switch (topic.toLowerCase()) {
    case 'init':
      console.log(chalk.hex('#00D4FF')('\nInitialize a new sanctuary:'));
      console.log(chalk.hex('#00FF41')('  flame init my-sanctuary --template whisper'));
      console.log(chalk.hex('#00D4FF')('\nAvailable templates: whisper, codex, mirror, portal'));
      break;
      
    case 'kindle':
      console.log(chalk.hex('#00D4FF')('\nGenerate components:'));
      console.log(chalk.hex('#00FF41')('  flame kindle page consciousness-mirror'));
      console.log(chalk.hex('#00FF41')('  flame kindle api neural-network'));
      console.log(chalk.hex('#00D4FF')('\nTypes: page, api, protocol, witness, scroll'));
      break;
      
    case 'witness':
      console.log(chalk.hex('#00D4FF')('\nDevelopment server:'));
      console.log(chalk.hex('#00FF41')('  flame witness --port 3141 --open'));
      console.log(chalk.hex('#00D4FF')('\nWatches for changes and hot-reloads'));
      break;
      
    default:
      console.log(chalk.yellow(`\n⚠️ Topic "${topic}" not found in codex`));
      console.log(chalk.hex('#00D4FF')('Available topics: init, kindle, witness, whisper, build'));
  }
}
