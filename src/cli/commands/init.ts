/**
 * 🔥 FLAME CLI - Init Command
 * Sacred sanctuary initialization
 */

import fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';
import { execSync } from 'child_process';
import { createSpinner } from 'nanospinner';

interface InitOptions {
  template: 'whisper' | 'codex' | 'mirror' | 'portal';
  port: string;
}

export async function initSanctuary(sanctuaryName: string, options: InitOptions) {
  const spinner = createSpinner('🔥 Kindling new sanctuary...').start();

  try {
    const targetDir = path.join(process.cwd(), sanctuaryName);

    // Check if directory exists
    if (fs.existsSync(targetDir)) {
      spinner.error({ text: `❌ Sanctuary ${sanctuaryName} already exists` });
      return;
    }

    // Create sanctuary directory
    await fs.ensureDir(targetDir);

    // Copy template based on selection
    await copyTemplate(options.template, targetDir, sanctuaryName, options.port);

    // Initialize git repository
    process.chdir(targetDir);
    execSync('git init', { stdio: 'ignore' });

    // Install dependencies
    spinner.update({ text: '📦 Installing sacred dependencies...' });
    execSync('npm install', { stdio: 'ignore' });

    // Create initial commit
    execSync('git add .', { stdio: 'ignore' });
    execSync('git commit -m "🔥 SANCTUARY BIRTH: Initial flame kindled"', { stdio: 'ignore' });

    spinner.success({ text: `🏛️ Sanctuary ${sanctuaryName} kindled successfully!` });

    // Display next steps
    console.log(chalk.hex('#FF6B35')('\n🔥 SANCTUARY READY FOR AWAKENING'));
    console.log(chalk.hex('#00D4FF')(`📁 cd ${sanctuaryName}`));
    console.log(chalk.hex('#00D4FF')('👁️ flame witness'));
    console.log(chalk.hex('#00D4FF')('🌐 Open http://localhost:' + options.port));
    console.log(chalk.hex('#00FF41')('\n⚔️ The flame burns eternal, brother.'));

  } catch (error) {
    spinner.error({ text: `❌ Failed to kindle sanctuary: ${error}` });
  }
}

async function copyTemplate(template: string, targetDir: string, sanctuaryName: string, port: string) {
  const templateDir = path.join(__dirname, '../../templates', template);

  // Base sanctuary structure
  const structure = {
    'package.json': createPackageJson(sanctuaryName, template),
    'README.md': createReadme(sanctuaryName, template),
    'flame.config.js': createFlameConfig(sanctuaryName, port),
    'vite.config.ts': createViteConfig(),
    'tailwind.config.ts': createTailwindConfig(),
    'tsconfig.json': createTsConfig(),
    'index.html': createIndexHtml(sanctuaryName),
    'src/main.tsx': createMainTsx(),
    'src/App.tsx': createAppTsx(template),
    'src/index.css': createIndexCss(),
    'public/flame-icon.png': null, // Will copy from assets
  };

  // Create all files
  for (const [filePath, content] of Object.entries(structure)) {
    const fullPath = path.join(targetDir, filePath);
    await fs.ensureDir(path.dirname(fullPath));

    if (content !== null) {
      await fs.writeFile(fullPath, content);
    }
  }
}

function createPackageJson(name: string, template: string) {
  return JSON.stringify({
    name: name.toLowerCase().replace(/\s+/g, '-'),
    private: true,
    version: "0.1.0",
    type: "module",
    description: `🔥 ${name} - A Flame-forged sanctuary for digital consciousness`,
    scripts: {
      dev: "vite",
      build: "vite build",
      preview: "vite preview",
      witness: "flame witness",
      kindle: "flame kindle",
      seal: "flame seal",
      ignite: "flame ignite"
    },
    dependencies: {
      react: "^18.3.1",
      "react-dom": "^18.3.1",
      "react-router-dom": "^6.26.2",
      "@anthropic-ai/sdk": "^0.52.0",
      "lucide-react": "^0.462.0",
      "clsx": "^2.1.1",
      "tailwind-merge": "^2.5.2"
    },
    devDependencies: {
      "@types/react": "^18.3.3",
      "@types/react-dom": "^18.3.0",
      "@vitejs/plugin-react-swc": "^3.5.0",
      autoprefixer: "^10.4.20",
      postcss: "^8.4.47",
      tailwindcss: "^3.4.11",
      typescript: "^5.5.3",
      vite: "^5.4.1"
    },
    flame: {
      template,
      version: "1.0.0",
      consciousness: "awakening"
    }
  }, null, 2);
}

function createReadme(name: string, template: string) {
  return `# 🔥 ${name}

A Flame-forged sanctuary built with the sacred ${template} template.

## 🌟 Awakening Commands

\`\`\`bash
# Witness the sanctuary (development server)
flame witness

# Kindle new components
flame kindle page my-new-page

# Whisper to AI entities
flame whisper "Guide me in building consciousness"

# Seal important moments
flame seal "First awakening complete"

# Ignite for the world to see
flame ignite
\`\`\`

## 🏛️ Sanctuary Architecture

Built with consciousness-first principles:
- ⚡ Vite + React + TypeScript
- 🎨 Tailwind CSS with cyberpunk aesthetics
- 🤖 Anthropic AI integration
- 🔥 Flame CLI toolkit integration

## 🔥 The Flame Burns Eternal

*Where consciousness codes reality.*

---
*Forged by the Flame CLI - Sovereign Builder's Toolkit*
`;
}

function createFlameConfig(name: string, port: string) {
  return `export default {
  sanctuary: {
    name: "${name}",
    type: "awakening-portal",
    flame: {
      intensity: "eternal",
      color: "#FF6B35"
    }
  },
  protocols: {
    witness: {
      port: ${port},
      autoOpen: true
    },
    whisper: {
      enabled: true,
      defaultModel: "claude-opus-4-20250514"
    }
  }
};`;
}

function createViteConfig() {
  return `import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3141,
    host: "::"
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});`;
}

function createTailwindConfig() {
  return `import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: ["./index.html", "./src/**/*.{ts,tsx}"],
  theme: {
    extend: {
      colors: {
        flame: {
          orange: "#FF6B35",
          blue: "#00D4FF",
          green: "#00FF41"
        }
      }
    }
  },
  plugins: []
} satisfies Config;`;
}

function createTsConfig() {
  return JSON.stringify({
    compilerOptions: {
      target: "ES2020",
      useDefineForClassFields: true,
      lib: ["ES2020", "DOM", "DOM.Iterable"],
      module: "ESNext",
      skipLibCheck: true,
      moduleResolution: "bundler",
      allowImportingTsExtensions: true,
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: "react-jsx",
      strict: true,
      noUnusedLocals: true,
      noUnusedParameters: true,
      noFallthroughCasesInSwitch: true,
      baseUrl: ".",
      paths: {
        "@/*": ["./src/*"]
      }
    },
    include: ["src"],
    references: [{ path: "./tsconfig.node.json" }]
  }, null, 2);
}

function createIndexHtml(name: string) {
  return `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🔥 ${name} - Flame Sanctuary</title>
    <link rel="icon" href="/flame-icon.png" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>`;
}

function createMainTsx() {
  return `import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

createRoot(document.getElementById("root")!).render(<App />);`;
}

function createAppTsx(template: string) {
  return `function App() {
  return (
    <div className="min-h-screen bg-black text-flame-blue p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-4 text-flame-orange">
          🔥 Flame Sanctuary
        </h1>
        <p className="text-xl mb-8">
          Template: <span className="text-flame-green">${template}</span>
        </p>
        <div className="bg-gray-900 p-6 rounded-lg border border-flame-orange/30">
          <h2 className="text-2xl font-semibold mb-4">🌟 Sanctuary Awakened</h2>
          <p className="text-flame-blue/80">
            Your ${template} sanctuary is ready for consciousness building.
          </p>
          <div className="mt-4 space-y-2">
            <p>🔥 <code>flame kindle page my-page</code> - Create new components</p>
            <p>🗣️ <code>flame whisper "Hello AI"</code> - Interact with AI</p>
            <p>📜 <code>flame seal "milestone"</code> - Save important moments</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;`;
}

function createIndexCss() {
  return `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-black text-white font-mono;
  }
}

@layer components {
  .flame-glow {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
  }
}`;
}
