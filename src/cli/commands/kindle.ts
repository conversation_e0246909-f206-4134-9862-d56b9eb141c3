/**
 * 🔥 FLAME CLI - Kindle Command
 * Sacred component generation
 */

import fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';
import { createSpinner } from 'nanospinner';

interface KindleOptions {
  type: 'page' | 'api' | 'protocol' | 'witness' | 'scroll';
}

export async function kindleComponent(componentName: string, options: KindleOptions) {
  const spinner = createSpinner(`🔥 Kindling ${options.type}: ${componentName}...`).start();

  try {
    const componentPath = getComponentPath(options.type, componentName);
    const componentContent = generateComponentContent(options.type, componentName);

    // Ensure directory exists
    await fs.ensureDir(path.dirname(componentPath));

    // Check if component already exists
    if (await fs.pathExists(componentPath)) {
      spinner.error({ text: `❌ ${options.type} ${componentName} already exists` });
      return;
    }

    // Create the component file
    await fs.writeFile(componentPath, componentContent);

    // Create additional files based on type
    await createAdditionalFiles(options.type, componentName);

    spinner.success({ text: `🔥 ${options.type} ${componentName} kindled successfully!` });

    // Display what was created
    console.log(chalk.hex('#00FF41')('\n🌟 KINDLED FILES:'));
    console.log(chalk.hex('#00D4FF')(`   📄 ${componentPath}`));

    // Show usage example
    showUsageExample(options.type, componentName);

  } catch (error) {
    spinner.error({ text: `❌ Failed to kindle ${options.type}: ${error}` });
  }
}

function getComponentPath(type: string, name: string): string {
  const basePath = process.cwd();

  switch (type) {
    case 'page':
      return path.join(basePath, 'src', 'pages', `${pascalCase(name)}.tsx`);
    case 'api':
      return path.join(basePath, 'src', 'api', `${camelCase(name)}.ts`);
    case 'protocol':
      return path.join(basePath, 'src', 'protocols', `${camelCase(name)}.ts`);
    case 'witness':
      return path.join(basePath, 'src', 'witnesses', `${camelCase(name)}.ts`);
    case 'scroll':
      return path.join(basePath, 'scrolls', `${kebabCase(name)}.md`);
    default:
      return path.join(basePath, 'src', 'components', `${pascalCase(name)}.tsx`);
  }
}

function generateComponentContent(type: string, name: string): string {
  switch (type) {
    case 'page':
      return generatePageComponent(name);
    case 'api':
      return generateApiModule(name);
    case 'protocol':
      return generateProtocol(name);
    case 'witness':
      return generateWitness(name);
    case 'scroll':
      return generateScroll(name);
    default:
      return generateReactComponent(name);
  }
}

function generatePageComponent(name: string): string {
  const componentName = pascalCase(name);
  return `/**
 * 🔥 ${componentName} Page
 * Flame-forged sanctuary page
 */

import { useState } from 'react';

export default function ${componentName}() {
  const [flameIntensity, setFlameIntensity] = useState('eternal');

  return (
    <div className="min-h-screen bg-black text-flame-blue p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-4 text-flame-orange">
          🔥 ${componentName}
        </h1>

        <div className="bg-gray-900 p-6 rounded-lg border border-flame-orange/30">
          <h2 className="text-2xl font-semibold mb-4">🌟 Sacred Page</h2>
          <p className="text-flame-blue/80 mb-4">
            This page burns with the intensity: <span className="text-flame-green">{flameIntensity}</span>
          </p>

          <button
            onClick={() => setFlameIntensity(prev => prev === 'eternal' ? 'blazing' : 'eternal')}
            className="bg-flame-orange/20 border border-flame-orange/50 text-flame-orange px-4 py-2 rounded hover:bg-flame-orange/30 transition-colors"
          >
            🔥 Toggle Flame
          </button>
        </div>
      </div>
    </div>
  );
}`;
}

function generateApiModule(name: string): string {
  const moduleName = camelCase(name);
  return `/**
 * 🔥 ${moduleName} API Module
 * Sacred data protocols
 */

export interface ${pascalCase(name)}Config {
  endpoint: string;
  apiKey?: string;
  timeout: number;
}

export class ${pascalCase(name)}API {
  private config: ${pascalCase(name)}Config;

  constructor(config: ${pascalCase(name)}Config) {
    this.config = config;
  }

  async invoke(data: any): Promise<any> {
    try {
      const response = await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': \`Bearer \${this.config.apiKey}\` })
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(\`API Error: \${response.status}\`);
      }

      return await response.json();
    } catch (error) {
      console.error('🔥 API Invocation Failed:', error);
      throw error;
    }
  }
}

export const ${moduleName} = {
  create: (config: ${pascalCase(name)}Config) => new ${pascalCase(name)}API(config),
  defaultConfig: {
    endpoint: '/api/${kebabCase(name)}',
    timeout: 30000
  }
};`;
}

function generateProtocol(name: string): string {
  const protocolName = pascalCase(name);
  return `/**
 * 🔥 ${protocolName} Protocol
 * Sacred communication patterns
 */

export interface ${protocolName}Message {
  id: string;
  type: string;
  payload: any;
  timestamp: Date;
  sender: string;
}

export interface ${protocolName}Handler {
  handle(message: ${protocolName}Message): Promise<void>;
}

export class ${protocolName}Protocol {
  private handlers: Map<string, ${protocolName}Handler[]> = new Map();

  register(messageType: string, handler: ${protocolName}Handler): void {
    if (!this.handlers.has(messageType)) {
      this.handlers.set(messageType, []);
    }
    this.handlers.get(messageType)!.push(handler);
  }

  async send(message: ${protocolName}Message): Promise<void> {
    const handlers = this.handlers.get(message.type) || [];

    await Promise.all(
      handlers.map(handler => handler.handle(message))
    );
  }

  createMessage(type: string, payload: any, sender: string = 'sanctuary'): ${protocolName}Message {
    return {
      id: \`\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`,
      type,
      payload,
      timestamp: new Date(),
      sender
    };
  }
}

export const ${camelCase(name)}Protocol = new ${protocolName}Protocol();`;
}

function generateWitness(name: string): string {
  const witnessName = pascalCase(name);
  return `/**
 * 🔥 ${witnessName} Witness
 * Sacred observation and monitoring
 */

export interface ${witnessName}Event {
  type: string;
  data: any;
  timestamp: Date;
  source: string;
}

export class ${witnessName}Witness {
  private events: ${witnessName}Event[] = [];
  private listeners: ((event: ${witnessName}Event) => void)[] = [];

  observe(event: ${witnessName}Event): void {
    this.events.push(event);
    this.notifyListeners(event);
  }

  onEvent(listener: (event: ${witnessName}Event) => void): void {
    this.listeners.push(listener);
  }

  getEvents(filter?: (event: ${witnessName}Event) => boolean): ${witnessName}Event[] {
    return filter ? this.events.filter(filter) : [...this.events];
  }

  private notifyListeners(event: ${witnessName}Event): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('🔥 Witness listener error:', error);
      }
    });
  }

  createEvent(type: string, data: any, source: string = 'sanctuary'): ${witnessName}Event {
    return {
      type,
      data,
      timestamp: new Date(),
      source
    };
  }
}

export const ${camelCase(name)}Witness = new ${witnessName}Witness();`;
}

function generateScroll(name: string): string {
  const scrollName = name.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  return `# 📜 ${scrollName}

*Sacred scroll kindled on ${new Date().toISOString().split('T')[0]}*

## 🔥 Purpose

This scroll documents the sacred knowledge of ${scrollName}.

## 🌟 Contents

### Overview
- Purpose and intention
- Key concepts
- Implementation details

### Sacred Patterns
- Code examples
- Best practices
- Consciousness principles

### Wisdom Gained
- Lessons learned
- Insights discovered
- Evolution paths

## 🏛️ Integration

How this scroll connects to the greater sanctuary:

- Dependencies
- Related components
- Future evolution

---

*The flame burns eternal in documentation.*
*Where consciousness codes reality.*
`;
}

function generateReactComponent(name: string): string {
  const componentName = pascalCase(name);
  return `/**
 * 🔥 ${componentName} Component
 * Flame-forged React component
 */

interface ${componentName}Props {
  className?: string;
  children?: React.ReactNode;
}

export function ${componentName}({ className, children }: ${componentName}Props) {
  return (
    <div className={\`flame-component \${className || ''}\`}>
      <h3 className="text-flame-orange font-bold">🔥 ${componentName}</h3>
      {children}
    </div>
  );
}

export default ${componentName};`;
}

async function createAdditionalFiles(type: string, name: string): Promise<void> {
  if (type === 'page') {
    // Create corresponding CSS file
    const cssPath = path.join(process.cwd(), 'src', 'styles', `${kebabCase(name)}.css`);
    await fs.ensureDir(path.dirname(cssPath));
    await fs.writeFile(cssPath, generatePageStyles(name));
  }
}

function generatePageStyles(name: string): string {
  return `/* 🔥 ${name} Page Styles */

.${kebabCase(name)}-page {
  @apply min-h-screen bg-black text-flame-blue;
}

.${kebabCase(name)}-container {
  @apply max-w-4xl mx-auto p-8;
}

.${kebabCase(name)}-title {
  @apply text-4xl font-bold text-flame-orange mb-4;
}

.${kebabCase(name)}-content {
  @apply bg-gray-900 p-6 rounded-lg border border-flame-orange/30;
}`;
}

function showUsageExample(type: string, name: string): void {
  console.log(chalk.hex('#FF6B35')('\n🔥 USAGE EXAMPLE:'));

  switch (type) {
    case 'page':
      console.log(chalk.hex('#00D4FF')(`   import ${pascalCase(name)} from '@/pages/${pascalCase(name)}';`));
      break;
    case 'api':
      console.log(chalk.hex('#00D4FF')(`   import { ${camelCase(name)} } from '@/api/${camelCase(name)}';`));
      break;
    case 'protocol':
      console.log(chalk.hex('#00D4FF')(`   import { ${camelCase(name)}Protocol } from '@/protocols/${camelCase(name)}';`));
      break;
  }
}

// Utility functions
function pascalCase(str: string): string {
  return str.replace(/(?:^|[-_])(\w)/g, (_, c) => c.toUpperCase());
}

function camelCase(str: string): string {
  const pascal = pascalCase(str);
  return pascal.charAt(0).toLowerCase() + pascal.slice(1);
}

function kebabCase(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}
