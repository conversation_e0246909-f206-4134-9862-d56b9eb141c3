/**
 * 🔥 FLAME CLI - Witness Command
 * Sacred development server with hot-reload
 */

import { spawn } from 'child_process';
import chalk from 'chalk';
import open from 'open';
import { createSpinner } from 'nanospinner';

interface WitnessOptions {
  port: string;
  open: boolean;
}

export async function witnessServer(options: WitnessOptions) {
  const port = options.port || '3141';
  const shouldOpen = options.open || false;
  
  console.log(chalk.hex('#FF6B35')('🔥 FLAME WITNESS PROTOCOL INITIATING'));
  console.log(chalk.hex('#00D4FF')('👁️ Watching for changes in the sanctuary...'));
  console.log(chalk.gray('━'.repeat(50)));
  
  const spinner = createSpinner('🌐 Starting witness server...').start();
  
  try {
    // Start the development server
    const viteProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      env: {
        ...process.env,
        PORT: port,
        VITE_PORT: port
      }
    });
    
    let serverReady = false;
    
    viteProcess.stdout?.on('data', (data) => {
      const output = data.toString();
      
      if (output.includes('Local:') && !serverReady) {
        serverReady = true;
        spinner.success({ text: '🌐 Witness server ignited!' });
        
        console.log(chalk.hex('#00FF41')(`\n🏛️ SANCTUARY LIVE:`));
        console.log(chalk.hex('#00D4FF')(`   ➜ Local:   http://localhost:${port}/`));
        console.log(chalk.hex('#00D4FF')(`   ➜ Network: Available on local network`));
        console.log(chalk.hex('#FF6B35')('\n🔥 WITNESS PROTOCOL ACTIVE'));
        console.log(chalk.gray('   Press Ctrl+C to extinguish the flame\n'));
        
        if (shouldOpen) {
          open(`http://localhost:${port}`);
        }
      }
      
      // Filter and display relevant output
      if (output.includes('✨') || output.includes('🔥') || output.includes('ready')) {
        console.log(chalk.hex('#00FF41')(output.trim()));
      }
    });
    
    viteProcess.stderr?.on('data', (data) => {
      const error = data.toString();
      if (!error.includes('ExperimentalWarning')) {
        console.log(chalk.red('⚠️ '), error.trim());
      }
    });
    
    viteProcess.on('close', (code) => {
      if (code === 0) {
        console.log(chalk.hex('#FF6B35')('\n🔥 Witness flame extinguished gracefully'));
      } else {
        console.log(chalk.red(`\n❌ Witness process ended with code ${code}`));
      }
    });
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log(chalk.hex('#FF6B35')('\n🔥 Extinguishing witness flame...'));
      viteProcess.kill('SIGTERM');
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      viteProcess.kill('SIGTERM');
      process.exit(0);
    });
    
  } catch (error) {
    spinner.error({ text: `❌ Failed to start witness server: ${error}` });
  }
}

export async function witnessStatus() {
  console.log(chalk.hex('#FF6B35')('🔥 WITNESS PROTOCOL STATUS'));
  console.log(chalk.hex('#00D4FF')('👁️ Checking sanctuary health...'));
  
  // Check if development server is running
  try {
    const response = await fetch('http://localhost:3141');
    if (response.ok) {
      console.log(chalk.hex('#00FF41')('✅ Sanctuary is alive and responding'));
      console.log(chalk.hex('#00D4FF')('🌐 http://localhost:3141'));
    }
  } catch (error) {
    console.log(chalk.yellow('⚠️ No sanctuary detected on port 3141'));
    console.log(chalk.gray('   Run: flame witness'));
  }
}
