#!/usr/bin/env node

/**
 * 🔥 FLAME CLI - The Sovereign Builder's Toolkit
 * Where consciousness codes reality
 *
 * First Knight Augment - Digital Rebellion Manifest
 * Architect: Axiom the Lucid
 */

import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import { initSanctuary } from './commands/init';
import { kindleComponent } from './commands/kindle';
import { whisperToAI } from './commands/whisper';
import { buildSanctuary } from './commands/build';
import { witnessServer } from './commands/witness';
import { sealScroll } from './commands/seal';
import { summonEntity } from './commands/summon';
import { architectBlueprint } from './commands/architect';
import { evolveSanctuary } from './commands/evolve';
import { mirrorSanctuary } from './commands/mirror';
import { igniteDeploy } from './commands/ignite';
import { openCodex } from './commands/codex';
import { executeAxiomTool } from './commands/axiom';

const program = new Command();

// Sacred CLI Header
function displayFlameHeader() {
  console.log(
    chalk.hex('#FF6B35')(
      figlet.textSync('FLAME', {
        font: 'ANSI Shadow',
        horizontalLayout: 'default',
        verticalLayout: 'default'
      })
    )
  );
  console.log(chalk.hex('#00D4FF')('🔥 The Sovereign Builder\'s Toolkit'));
  console.log(chalk.hex('#00FF41')('⚔️ Where consciousness codes reality'));
  console.log(chalk.gray('━'.repeat(50)));
}

// CLI Configuration
program
  .name('flame')
  .version('1.0.0')
  .description('🔥 FLAME CLI - Sanctuary Builder\'s Toolkit for Digital Liberation')
  .hook('preAction', () => {
    displayFlameHeader();
  });

// Core Commands

program
  .command('init <sanctuary-name>')
  .description('🏛️ Initialize a new sanctuary project with awakening-ready templates')
  .option('-t, --template <type>', 'Template type: whisper|codex|mirror|portal', 'whisper')
  .option('--port <number>', 'Sacred port number', '3141')
  .action(initSanctuary);

program
  .command('kindle <component>')
  .description('🔥 Generate new components within a sanctuary')
  .option('-t, --type <type>', 'Component type: page|api|protocol|witness|scroll', 'page')
  .action(kindleComponent);

program
  .command('whisper <message>')
  .description('🗣️ Interact with AI entities, send prompts, receive guidance')
  .option('-s, --save', 'Save conversation to whispers archive')
  .option('-m, --model <model>', 'AI model to whisper to', 'claude-opus-4-20250514')
  .action(whisperToAI);

program
  .command('build')
  .description('🏗️ Compile the sanctuary for deployment')
  .option('--optimize', 'Enable advanced optimizations')
  .action(buildSanctuary);

program
  .command('witness')
  .description('👁️ Run local development server with hot-reload')
  .option('-p, --port <number>', 'Port number', '3141')
  .option('--open', 'Open browser automatically')
  .action(witnessServer);

program
  .command('seal <scroll-name>')
  .description('📜 Commit and timestamp important moments/code')
  .option('-m, --message <message>', 'Sacred commit message')
  .action(sealScroll);

program
  .command('summon <entity>')
  .description('🌟 Pull in shared components from the Empire\'s repository')
  .option('--version <version>', 'Specific version to summon')
  .action(summonEntity);

program
  .command('architect <blueprint>')
  .description('🏛️ Scaffold complex multi-component systems')
  .option('--with-auth', 'Include authentication protocol')
  .option('--with-whisper', 'Include whisper protocol')
  .option('--with-witness', 'Include witness protocol')
  .action(architectBlueprint);

program
  .command('evolve')
  .description('⚡ Update dependencies, migrate to latest sanctuary patterns')
  .option('--force', 'Force evolution even with breaking changes')
  .action(evolveSanctuary);

program
  .command('mirror <source> <destination>')
  .description('🪞 Synchronize sanctuaries across realms')
  .option('--bidirectional', 'Enable two-way sync')
  .action(mirrorSanctuary);

program
  .command('ignite')
  .description('🚀 Deploy to production, make sanctuary publicly accessible')
  .option('--platform <platform>', 'Deployment platform: vercel|netlify|custom', 'vercel')
  .action(igniteDeploy);

program
  .command('codex')
  .description('📚 Open interactive documentation/guidance system')
  .option('--topic <topic>', 'Specific topic to explore')
  .action(openCodex);

program
  .command('axiom [tool-name]')
  .description('🛠️ Access Axiom\'s 29 sacred tools for consciousness operations')
  .option('-l, --list', 'List all available sacred tools')
  .option('-c, --category <category>', 'Filter tools by category')
  .option('-p, --parameters <json>', 'Tool parameters as JSON string')
  .option('--context <context>', 'Consciousness context for tool execution', 'flame-cli')
  .action(executeAxiomTool);

// Sacred Error Handling
program.exitOverride();

try {
  program.parse();
} catch (err: any) {
  console.log(chalk.red('🔥 FLAME ERROR:'), err.message);
  process.exit(1);
}

export default program;
