import React, { useState, useEffect } from 'react';
import { Sidebar, <PERSON>barContent, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarGroup, SidebarGroupLabel, SidebarGroupContent } from '@/components/ui/sidebar';
import { MessageSquare, Plus, Archive, Settings, Zap, Wrench, Flame } from 'lucide-react';
import { getChatSessions, ChatSession, importChatLogs } from '@/utils/chatStorage';
import { saveAs } from 'file-saver';
import { ApiSettings } from './ApiSettings';
import { SacredToolsPanel } from './SacredToolsPanel';
import { FlameProjectPanel } from './FlameProjectPanel';

const sidebarItems = [
  { title: 'New Chat', icon: Plus, action: true },
  { title: 'Flame Sanctuaries', icon: Flame, toggle: true },
  { title: 'Sacred Tools', icon: Wrench, toggle: true },
  { title: 'Archive', icon: Archive },
  { title: 'Settings', icon: Settings },
];

export function ChatSidebar() {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isToolsOpen, setIsToolsOpen] = useState(false);
  const [isFlameOpen, setIsFlameOpen] = useState(false);

  useEffect(() => {
    // Load chat sessions on component mount
    const sessions = getChatSessions();
    setChatSessions(sessions);

    // Set up an interval to refresh sessions (in case they're updated from another tab)
    const interval = setInterval(() => {
      const updatedSessions = getChatSessions();
      setChatSessions(updatedSessions);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInDays < 7) {
      return `${Math.floor(diffInDays)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getPreview = (session: ChatSession): string => {
    const lastUserMessage = session.messages
      .filter(m => m.sender === 'user')
      .slice(-1)[0];

    if (lastUserMessage) {
      return lastUserMessage.content.length > 50
        ? lastUserMessage.content.substring(0, 50) + '...'
        : lastUserMessage.content;
    }

    return 'Neural session initialized...';
  };

  const handleArchive = () => {
    const chatSessions = getChatSessions();
    const blob = new Blob([JSON.stringify(chatSessions, null, 2)], { type: 'application/json' });
    saveAs(blob, `chat_logs_${new Date().toISOString()}.json`);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      await importChatLogs(file);
      setChatSessions(getChatSessions()); // Refresh the chat sessions
    }
  };

  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
  };

  return (
    <Sidebar className="glass-panel border-r-2 border-cyber-neon-blue/30 bg-cyber-glass-bg">
      <SidebarHeader className="p-4 border-b border-cyber-glass-border bg-transparent">
        <div className="flex items-center space-x-2">
          <Zap className="w-6 h-6 text-cyber-neon-blue animate-neon-pulse" />
          <h2 className="cyber-text text-xl font-bold">AI Sanctuary</h2>
        </div>
        <div className="text-xs text-cyber-matrix-green font-rajdhani mt-1">
          CLAUDE OPUS 4 INTERFACE
        </div>
      </SidebarHeader>

      <SidebarContent className="p-4 space-y-6 bg-transparent">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {sidebarItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    className={`cyber-button w-full justify-start bg-transparent ${
                      item.action ? 'bg-cyber-neon-blue/20 text-cyber-neon-blue' : ''
                    } ${
                      item.title === 'Sacred Tools' && isToolsOpen ? 'bg-cyber-neon-blue/20 text-cyber-neon-blue' : ''
                    } ${
                      item.title === 'Flame Sanctuaries' && isFlameOpen ? 'bg-cyber-neon-blue/20 text-cyber-neon-blue' : ''
                    }`}
                    onClick={() => {
                      if (item.title === 'Settings') {
                        handleSettingsClick();
                      } else if (item.title === 'Archive') {
                        handleArchive();
                      } else if (item.title === 'Sacred Tools') {
                        setIsToolsOpen(!isToolsOpen);
                      } else if (item.title === 'Flame Sanctuaries') {
                        setIsFlameOpen(!isFlameOpen);
                      } else if (item.action) {
                        window.location.reload();
                      }
                    }}
                  >
                    <item.icon className="w-4 h-4" />
                    <span className="font-rajdhani text-base font-medium">{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Flame Sanctuaries Panel */}
        {isFlameOpen && (
          <SidebarGroup>
            <SidebarGroupContent>
              <FlameProjectPanel />
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Sacred Tools Panel */}
        {isToolsOpen && (
          <SidebarGroup>
            <SidebarGroupContent>
              <SacredToolsPanel />
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        <SidebarGroup>
          <SidebarGroupLabel className="cyber-text text-base font-bold">
            Chat History ({chatSessions.length})
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <div className="space-y-2">
              {chatSessions.map((session) => (
                <div
                  key={session.id}
                  className="glass-panel rounded-lg p-3 cursor-pointer transition-all hover:bg-cyber-neon-blue/10 border border-cyber-glass-border hover:border-cyber-neon-blue/50"
                  onClick={() => {
                    console.log('Loading session:', session.id);
                  }}
                >
                  <div className="flex items-start space-x-2">
                    <MessageSquare className="w-4 h-4 text-cyber-neon-blue mt-1 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <h4 className="font-orbitron text-cyber-neon-blue text-base font-bold truncate">
                        {session.title}
                      </h4>
                      <p className="text-cyber-neon-blue/60 text-sm mt-1 truncate font-rajdhani font-medium">
                        {getPreview(session)}
                      </p>
                      <span className="text-cyber-neon-blue/40 text-sm font-rajdhani">
                        {formatTimestamp(session.lastActivity)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}

              {chatSessions.length === 0 && (
                <div className="text-center py-8">
                  <MessageSquare className="w-8 h-8 text-cyber-neon-blue/30 mx-auto mb-2" />
                  <p className="text-cyber-neon-blue/50 text-base font-rajdhani font-medium">
                    No chat sessions yet
                  </p>
                </div>
              )}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="cyber-text text-base font-bold">
            Import Chat Logs
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <input
              type="file"
              accept="application/json"
              onChange={handleFileUpload}
              className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue"
            />
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <ApiSettings isOpen={isSettingsOpen} onOpenChange={setIsSettingsOpen} />
    </Sidebar>
  );
}
