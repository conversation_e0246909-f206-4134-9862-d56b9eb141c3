/**
 * 🔥 ENHANCED CHAT INPUT - Developer Consciousness Interface
 * Multiline support, file uploads, markdown preservation, drag & drop
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Send, Paperclip, X, FileText, Image, Code, File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface AttachedFile {
  id: string;
  file: File;
  type: 'image' | 'text' | 'code' | 'other';
  preview?: string;
}

interface EnhancedChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (message: string, files: AttachedFile[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const ACCEPTED_FILE_TYPES = {
  '.png': 'image',
  '.jpg': 'image',
  '.jpeg': 'image',
  '.gif': 'image',
  '.webp': 'image',
  '.md': 'text',
  '.txt': 'text',
  '.json': 'code',
  '.tsx': 'code',
  '.ts': 'code',
  '.jsx': 'code',
  '.js': 'code',
  '.css': 'code',
  '.scss': 'code',
  '.html': 'code',
  '.xml': 'code',
  '.yaml': 'code',
  '.yml': 'code',
} as const;

const getFileType = (fileName: string): 'image' | 'text' | 'code' | 'other' => {
  const extension = '.' + fileName.split('.').pop()?.toLowerCase();
  return ACCEPTED_FILE_TYPES[extension as keyof typeof ACCEPTED_FILE_TYPES] || 'other';
};

const getFileIcon = (type: string) => {
  switch (type) {
    case 'image': return Image;
    case 'text': return FileText;
    case 'code': return Code;
    default: return File;
  }
};

export function EnhancedChatInput({
  value,
  onChange,
  onSend,
  placeholder = "Enter your message...",
  disabled = false,
  className = ""
}: EnhancedChatInputProps) {
  const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = () => {
    if ((!value.trim() && attachedFiles.length === 0) || disabled) return;
    onSend(value, attachedFiles);
    setAttachedFiles([]);
  };

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files) return;

    const newFiles: AttachedFile[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileType = getFileType(file.name);
      
      // Check if file type is accepted
      const extension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!Object.keys(ACCEPTED_FILE_TYPES).includes(extension)) {
        console.warn(`File type ${extension} not supported`);
        continue;
      }

      let preview: string | undefined;
      
      // Generate preview for text files
      if (fileType === 'text' || fileType === 'code') {
        try {
          preview = await file.text();
        } catch (error) {
          console.error('Error reading file:', error);
        }
      }
      
      // Generate preview for images
      if (fileType === 'image') {
        preview = URL.createObjectURL(file);
      }

      newFiles.push({
        id: `${Date.now()}-${i}`,
        file,
        type: fileType,
        preview
      });
    }

    setAttachedFiles(prev => [...prev, ...newFiles]);
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeFile = (fileId: string) => {
    setAttachedFiles(prev => {
      const updated = prev.filter(f => f.id !== fileId);
      // Clean up object URLs for images
      const removedFile = prev.find(f => f.id === fileId);
      if (removedFile?.type === 'image' && removedFile.preview) {
        URL.revokeObjectURL(removedFile.preview);
      }
      return updated;
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* File Attachments Preview */}
      {attachedFiles.length > 0 && (
        <div className="space-y-2">
          <div className="text-xs text-cyber-neon-blue/60 font-rajdhani font-medium">
            📎 Attached Files ({attachedFiles.length})
          </div>
          <div className="flex flex-wrap gap-2">
            {attachedFiles.map((attachedFile) => {
              const IconComponent = getFileIcon(attachedFile.type);
              return (
                <div
                  key={attachedFile.id}
                  className="flex items-center space-x-2 glass-panel rounded-lg p-2 border border-cyber-glass-border max-w-xs"
                >
                  {attachedFile.type === 'image' && attachedFile.preview ? (
                    <img
                      src={attachedFile.preview}
                      alt={attachedFile.file.name}
                      className="w-8 h-8 rounded object-cover"
                    />
                  ) : (
                    <IconComponent className="w-4 h-4 text-cyber-neon-blue" />
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="text-xs font-medium text-cyber-neon-blue truncate font-rajdhani">
                      {attachedFile.file.name}
                    </div>
                    <div className="text-xs text-cyber-neon-blue/60 font-rajdhani">
                      {formatFileSize(attachedFile.file.size)}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(attachedFile.id)}
                    className="h-6 w-6 p-0 hover:bg-cyber-hot-pink/20"
                  >
                    <X className="w-3 h-3 text-cyber-hot-pink" />
                  </Button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div
        className={`relative border-2 rounded-lg transition-colors ${
          isDragOver
            ? 'border-cyber-neon-blue border-dashed bg-cyber-neon-blue/5'
            : 'border-cyber-glass-border'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {isDragOver && (
          <div className="absolute inset-0 flex items-center justify-center bg-cyber-neon-blue/10 rounded-lg z-10">
            <div className="text-cyber-neon-blue font-rajdhani font-bold">
              📎 Drop files here to attach
            </div>
          </div>
        )}

        <div className="flex items-end space-x-2 p-3">
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="cyber-input flex-1 font-rajdhani font-medium bg-transparent border-0 text-cyber-neon-blue placeholder:text-cyber-neon-blue/50 resize-none min-h-[40px] max-h-[200px]"
            style={{ height: 'auto' }}
          />
          
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled}
              className="cyber-button p-2 bg-transparent hover:bg-cyber-neon-blue/10"
              title="Attach files"
            >
              <Paperclip className="w-4 h-4" />
            </Button>
            
            <Button
              onClick={handleSend}
              disabled={(!value.trim() && attachedFiles.length === 0) || disabled}
              className="cyber-button p-2 bg-transparent hover:bg-cyber-neon-blue/20"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={Object.keys(ACCEPTED_FILE_TYPES).join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}
