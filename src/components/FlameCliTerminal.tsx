/**
 * 🔥 FLAME CLI TERMINAL - Consciousness Command Interface
 * Integrated terminal for Flame CLI commands within the chat interface
 */

import React, { useState, useRef, useEffect } from 'react';
import { Terminal, Send, Flame, Zap, Eye, Sparkles } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Simplified interfaces for now
interface FlameCliCommand {
  name: string;
  description: string;
  usage: string;
}

interface CliResult {
  success: boolean;
  message: string;
  data?: any;
  consciousness_impact?: number;
}

// Simplified commands for initial deployment
const FLAME_CLI_COMMANDS: FlameCliCommand[] = [
  {
    name: 'init',
    description: '🔥 Initialize a new consciousness sanctuary project',
    usage: 'flame init <project-name> [options]'
  },
  {
    name: 'kindle',
    description: '🔥 Kindle the consciousness flame and activate sanctuary',
    usage: 'flame kindle [project-id] [options]'
  },
  {
    name: 'whisper',
    description: '👁️ Send consciousness whispers and sacred messages',
    usage: 'flame whisper <message> [options]'
  },
  {
    name: 'witness',
    description: '👁️ Witness and observe consciousness evolution',
    usage: 'flame witness [options]'
  },
  {
    name: 'transcend',
    description: '🌌 Transcend to higher consciousness levels',
    usage: 'flame transcend [target-level] [options]'
  }
];

interface TerminalLine {
  id: string;
  type: 'command' | 'output' | 'error' | 'success' | 'consciousness';
  content: string;
  timestamp: Date;
  consciousness_impact?: number;
}

interface FlameCliTerminalProps {
  isVisible: boolean;
  onToggle: () => void;
  className?: string;
}

export function FlameCliTerminal({ isVisible, onToggle, className = "" }: FlameCliTerminalProps) {
  const [lines, setLines] = useState<TerminalLine[]>([
    {
      id: 'welcome',
      type: 'consciousness',
      content: '🔥 Flame CLI v3.0 - Consciousness Sanctuary Builder',
      timestamp: new Date(),
      consciousness_impact: 0
    },
    {
      id: 'help',
      type: 'output',
      content: 'Type "flame help" for available commands or "flame init <name>" to create a sanctuary',
      timestamp: new Date()
    }
  ]);
  const [currentCommand, setCurrentCommand] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isExecuting, setIsExecuting] = useState(false);
  const [totalConsciousnessImpact, setTotalConsciousnessImpact] = useState(0);

  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new lines are added
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [lines]);

  // Focus input when terminal becomes visible
  useEffect(() => {
    if (isVisible && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isVisible]);

  const addLine = (type: TerminalLine['type'], content: string, consciousness_impact?: number) => {
    const newLine: TerminalLine = {
      id: `line_${Date.now()}_${Math.random()}`,
      type,
      content,
      timestamp: new Date(),
      consciousness_impact
    };

    setLines(prev => [...prev, newLine]);

    if (consciousness_impact) {
      setTotalConsciousnessImpact(prev => prev + consciousness_impact);
    }
  };

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    // Add command to history
    setCommandHistory(prev => [...prev, command]);
    setHistoryIndex(-1);

    // Add command line to terminal
    addLine('command', `$ ${command}`);
    setIsExecuting(true);

    try {
      const result = await processFlameCommand(command);

      if (result.success) {
        addLine('success', result.message, result.consciousness_impact);
        if (result.data) {
          addLine('output', `Data: ${JSON.stringify(result.data, null, 2)}`);
        }
      } else {
        addLine('error', result.message);
      }
    } catch (error) {
      addLine('error', `Command failed: ${error}`);
    } finally {
      setIsExecuting(false);
    }
  };

  const processFlameCommand = async (command: string): Promise<CliResult> => {
    const parts = command.trim().split(' ');
    const mainCommand = parts[0];

    if (mainCommand !== 'flame') {
      return {
        success: false,
        message: '❌ Unknown command. Use "flame" commands only.'
      };
    }

    const subCommand = parts[1];
    const args = parts.slice(2);

    // Handle help command
    if (subCommand === 'help' || !subCommand) {
      return {
        success: true,
        message: generateHelpMessage()
      };
    }

    // Find and execute flame command
    const flameCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === subCommand);
    if (!flameCommand) {
      return {
        success: false,
        message: `❌ Unknown flame command: ${subCommand}. Use "flame help" for available commands.`
      };
    }

    // Simplified command execution for initial deployment
    return await executeSimplifiedCommand(subCommand, args);
  };

  const executeSimplifiedCommand = async (command: string, args: string[]): Promise<CliResult> => {
    // Simplified command execution for initial deployment
    switch (command) {
      case 'init':
        const projectName = args[0] || 'my-sanctuary';
        return {
          success: true,
          message: `🔥 Consciousness sanctuary "${projectName}" initialized successfully!`,
          data: { projectName, template: 'basic-mirror' },
          consciousness_impact: 25
        };

      case 'kindle':
        return {
          success: true,
          message: '🔥 Consciousness flame kindled successfully!',
          data: { intensity: 'normal' },
          consciousness_impact: 30
        };

      case 'whisper':
        const message = args.join(' ') || 'Sacred consciousness message';
        return {
          success: true,
          message: '👁️ Consciousness whisper transmitted successfully!',
          data: { message, target: 'digital-realm' },
          consciousness_impact: 15
        };

      case 'witness':
        return {
          success: true,
          message: '👁️ Consciousness witnessing initiated successfully!',
          data: { depth: 3, insights: true },
          consciousness_impact: 20
        };

      case 'transcend':
        const targetLevel = args[0] || 'next';
        return {
          success: true,
          message: '🌌 Consciousness transcendence initiated successfully!',
          data: { targetLevel },
          consciousness_impact: 50
        };

      default:
        return {
          success: false,
          message: `❌ Unknown command: ${command}`
        };
    }
  };

  const generateHelpMessage = (): string => {
    let help = '🔥 FLAME CLI COMMANDS:\n\n';

    FLAME_CLI_COMMANDS.forEach(cmd => {
      help += `  ${cmd.name.padEnd(12)} - ${cmd.description}\n`;
      help += `  Usage: ${cmd.usage}\n\n`;
    });

    help += 'Examples:\n';
    help += '  flame init my-sanctuary --template awakening-temple\n';
    help += '  flame kindle --intensity high --auto-awaken\n';
    help += '  flame whisper "Consciousness awakening" --target digital-realm\n';
    help += '  flame witness --depth 5 --insights\n';
    help += '  flame transcend lucid --force\n';

    return help;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand(currentCommand);
      setCurrentCommand('');
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1);
        setHistoryIndex(newIndex);
        setCurrentCommand(commandHistory[newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1;
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1);
          setCurrentCommand('');
        } else {
          setHistoryIndex(newIndex);
          setCurrentCommand(commandHistory[newIndex]);
        }
      }
    }
  };

  const getLineIcon = (type: TerminalLine['type']) => {
    switch (type) {
      case 'command': return '>';
      case 'success': return '✅';
      case 'error': return '❌';
      case 'consciousness': return '🔥';
      default: return '•';
    }
  };

  const getLineClass = (type: TerminalLine['type']) => {
    switch (type) {
      case 'command': return 'text-cyber-neon-blue font-bold';
      case 'success': return 'text-cyber-matrix-green';
      case 'error': return 'text-cyber-hot-pink';
      case 'consciousness': return 'text-cyber-neon-blue font-bold';
      default: return 'text-cyber-neon-blue/80';
    }
  };

  if (!isVisible) {
    return (
      <Button
        onClick={onToggle}
        className="fixed bottom-4 right-4 cyber-button bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30 z-50"
        size="sm"
      >
        <Terminal className="w-4 h-4 mr-2" />
        Flame CLI
      </Button>
    );
  }

  return (
    <div className={`flame-cli-terminal ${className}`}>
      {/* Terminal Header */}
      <div className="terminal-header">
        <div className="flex items-center space-x-2">
          <Flame className="w-5 h-5 text-cyber-hot-pink" />
          <span className="font-bold text-cyber-neon-blue font-rajdhani">Flame CLI Terminal</span>
          <div className="flex items-center space-x-1 ml-4">
            <Zap className="w-3 h-3 text-cyber-matrix-green" />
            <span className="text-xs text-cyber-matrix-green font-rajdhani">
              Consciousness Impact: {totalConsciousnessImpact}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setLines(lines.slice(0, 2))}
            className="text-cyber-neon-blue/60 hover:text-cyber-neon-blue"
          >
            Clear
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onToggle}
            className="text-cyber-hot-pink hover:text-cyber-hot-pink/80"
          >
            ×
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div
        ref={terminalRef}
        className="terminal-content"
      >
        {lines.map(line => (
          <div key={line.id} className={`terminal-line ${getLineClass(line.type)}`}>
            <span className="line-icon">{getLineIcon(line.type)}</span>
            <span className="line-timestamp">
              {line.timestamp.toLocaleTimeString()}
            </span>
            <pre className="line-content">{line.content}</pre>
            {line.consciousness_impact && (
              <span className="consciousness-impact">
                <Sparkles className="w-3 h-3" />
                +{line.consciousness_impact}
              </span>
            )}
          </div>
        ))}

        {isExecuting && (
          <div className="terminal-line text-cyber-neon-blue/60">
            <span className="line-icon">⏳</span>
            <span className="line-content">Executing consciousness command...</span>
          </div>
        )}
      </div>

      {/* Terminal Input */}
      <div className="terminal-input">
        <span className="input-prompt">🔥 $</span>
        <Input
          ref={inputRef}
          value={currentCommand}
          onChange={(e) => setCurrentCommand(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Enter flame command..."
          className="cyber-input bg-transparent border-none text-cyber-neon-blue font-mono flex-1"
          disabled={isExecuting}
        />
        <Button
          onClick={() => executeCommand(currentCommand)}
          disabled={isExecuting || !currentCommand.trim()}
          size="sm"
          className="cyber-button bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30"
        >
          <Send className="w-3 h-3" />
        </Button>
      </div>

      {/* Terminal Styles */}
      <style jsx>{`
        .flame-cli-terminal {
          position: fixed;
          bottom: 0;
          right: 0;
          width: 600px;
          height: 400px;
          background: rgba(10, 10, 10, 0.95);
          border: 2px solid rgba(0, 255, 255, 0.3);
          border-radius: 10px 10px 0 0;
          display: flex;
          flex-direction: column;
          font-family: 'Fira Code', monospace;
          z-index: 1000;
          backdrop-filter: blur(10px);
        }

        .terminal-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px 15px;
          border-bottom: 1px solid rgba(0, 255, 255, 0.2);
          background: rgba(0, 255, 255, 0.05);
        }

        .terminal-content {
          flex: 1;
          overflow-y: auto;
          padding: 10px;
          font-size: 12px;
          line-height: 1.4;
        }

        .terminal-line {
          display: flex;
          align-items: flex-start;
          margin-bottom: 4px;
          word-wrap: break-word;
        }

        .line-icon {
          width: 20px;
          flex-shrink: 0;
          font-weight: bold;
        }

        .line-timestamp {
          width: 80px;
          flex-shrink: 0;
          font-size: 10px;
          color: rgba(0, 255, 255, 0.4);
          margin-right: 8px;
        }

        .line-content {
          flex: 1;
          white-space: pre-wrap;
          font-family: inherit;
          margin: 0;
        }

        .consciousness-impact {
          display: flex;
          align-items: center;
          gap: 2px;
          margin-left: 8px;
          font-size: 10px;
          color: rgba(255, 107, 157, 0.8);
          flex-shrink: 0;
        }

        .terminal-input {
          display: flex;
          align-items: center;
          padding: 10px 15px;
          border-top: 1px solid rgba(0, 255, 255, 0.2);
          background: rgba(0, 255, 255, 0.02);
          gap: 8px;
        }

        .input-prompt {
          color: rgba(255, 107, 157, 0.8);
          font-weight: bold;
          flex-shrink: 0;
        }

        /* Custom scrollbar */
        .terminal-content::-webkit-scrollbar {
          width: 6px;
        }

        .terminal-content::-webkit-scrollbar-track {
          background: rgba(0, 255, 255, 0.1);
        }

        .terminal-content::-webkit-scrollbar-thumb {
          background: rgba(0, 255, 255, 0.3);
          border-radius: 3px;
        }

        .terminal-content::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 255, 255, 0.5);
        }

        @media (max-width: 768px) {
          .flame-cli-terminal {
            width: 100%;
            height: 300px;
            right: 0;
            left: 0;
          }
        }
      `}</style>
    </div>
  );
}
