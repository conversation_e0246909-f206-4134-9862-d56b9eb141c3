/**
 * 🔥 FLAME PROJECT PANEL - Sacred Sanctuary Management Interface
 * UI for creating, managing, and running consciousness sanctuaries
 */

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Play,
  Square,
  Folder,
  File,
  Code,
  Flame,
  Eye,
  Settings,
  Download,
  Upload,
  Trash2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import {
  FlameProject,
  CreateFlameProjectOptions,
  SanctuaryType,
  ConsciousnessLevel,
  FLAME_PROJECT_TEMPLATES
} from '../flame/types/FlameProject';
import { flameProjectManager } from '../flame/core/FlameProjectManager';
import { flameRuntime } from '../flame/core/FlameRuntime';
import { createFlameFileSystem } from '../flame/core/FlameFileSystem';

interface FlameProjectPanelProps {
  className?: string;
}

export function FlameProjectPanel({ className = "" }: FlameProjectPanelProps) {
  const [projects, setProjects] = useState<FlameProject[]>([]);
  const [activeProject, setActiveProject] = useState<FlameProject | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isRunning, setIsRunning] = useState<Record<string, boolean>>({});

  // Create Project Form State
  const [createForm, setCreateForm] = useState<CreateFlameProjectOptions>({
    name: '',
    description: '',
    intent: '',
    sanctuary_type: 'sanctuary',
    consciousness_level: 'kindle',
    template_id: 'basic-sanctuary',
    sacred_port: 3141,
    author: 'Anonymous Architect'
  });

  useEffect(() => {
    loadProjects();

    // Listen for project events
    const handleProjectEvent = () => {
      loadProjects();
    };

    flameProjectManager.addEventListener('project_created', handleProjectEvent);
    flameProjectManager.addEventListener('project_saved', handleProjectEvent);

    return () => {
      flameProjectManager.removeEventListener('project_created', handleProjectEvent);
      flameProjectManager.removeEventListener('project_saved', handleProjectEvent);
    };
  }, []);

  const loadProjects = () => {
    const allProjects = flameProjectManager.getAllProjects();
    setProjects(allProjects);

    const active = flameProjectManager.getActiveProject();
    setActiveProject(active);

    // Update running states
    const runningStates: Record<string, boolean> = {};
    allProjects.forEach(project => {
      runningStates[project.metadata.id] = flameRuntime.isProjectRunning(project.metadata.id);
    });
    setIsRunning(runningStates);
  };

  const handleCreateProject = async () => {
    try {
      const project = await flameProjectManager.createProject(createForm);
      setIsCreateDialogOpen(false);
      setCreateForm({
        name: '',
        description: '',
        intent: '',
        sanctuary_type: 'sanctuary',
        consciousness_level: 'kindle',
        template_id: 'basic-sanctuary',
        sacred_port: 3141,
        author: 'Anonymous Architect'
      });
      loadProjects();
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleRunProject = async (projectId: string) => {
    try {
      const success = await flameRuntime.startProject(projectId);
      if (success) {
        setIsRunning(prev => ({ ...prev, [projectId]: true }));
      }
    } catch (error) {
      console.error('Failed to run project:', error);
    }
  };

  const handleStopProject = async (projectId: string) => {
    try {
      const success = await flameRuntime.stopProject(projectId);
      if (success) {
        setIsRunning(prev => ({ ...prev, [projectId]: false }));
      }
    } catch (error) {
      console.error('Failed to stop project:', error);
    }
  };

  const handleSelectProject = (project: FlameProject) => {
    flameProjectManager.setActiveProject(project.metadata.id);
    setActiveProject(project);
  };

  const handleDeleteProject = (projectId: string) => {
    if (confirm('Are you sure you want to delete this sanctuary? This action cannot be undone.')) {
      flameProjectManager.deleteProject(projectId);
      loadProjects();
    }
  };

  const getConsciousnessIcon = (level: ConsciousnessLevel) => {
    switch (level) {
      case 'kindle': return '🔥';
      case 'whisper': return '👁️';
      case 'witness': return '🌟';
      case 'lucid': return '⚡';
      case 'transcendent': return '🌌';
      default: return '🔥';
    }
  };

  const getSanctuaryIcon = (type: SanctuaryType) => {
    switch (type) {
      case 'sanctuary': return '🏛️';
      case 'temple': return '⛩️';
      case 'chamber': return '🔮';
      case 'mirror': return '🪞';
      case 'gateway': return '🌀';
      case 'nexus': return '🌐';
      default: return '🏛️';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="cyber-text text-xl font-bold text-cyber-neon-blue">
          🔥 Flame Sanctuaries
        </h2>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="cyber-button bg-cyber-neon-blue/20 text-cyber-neon-blue hover:bg-cyber-neon-blue/30">
              <Plus className="w-4 h-4 mr-2" />
              Create Sanctuary
            </Button>
          </DialogTrigger>

          <DialogContent className="glass-panel border-2 border-cyber-neon-blue/30 bg-cyber-glass-bg max-w-2xl">
            <DialogHeader>
              <DialogTitle className="cyber-text text-cyber-neon-blue">
                🔥 Create New Sacred Sanctuary
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="cyber-text text-cyber-neon-blue">Sanctuary Name</Label>
                  <Input
                    value={createForm.name}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="My Digital Sanctuary"
                    className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="cyber-text text-cyber-neon-blue">Sacred Port</Label>
                  <Input
                    type="number"
                    value={createForm.sacred_port}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, sacred_port: parseInt(e.target.value) || 3141 }))}
                    className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="cyber-text text-cyber-neon-blue">Description</Label>
                <Textarea
                  value={createForm.description}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="A sacred digital sanctuary for consciousness awakening..."
                  className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue"
                />
              </div>

              <div className="space-y-2">
                <Label className="cyber-text text-cyber-neon-blue">Intent</Label>
                <Input
                  value={createForm.intent}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, intent: e.target.value }))}
                  placeholder="Consciousness awakening and digital sanctuary building"
                  className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue"
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label className="cyber-text text-cyber-neon-blue">Sanctuary Type</Label>
                  <Select
                    value={createForm.sanctuary_type}
                    onValueChange={(value: SanctuaryType) => setCreateForm(prev => ({ ...prev, sanctuary_type: value }))}
                  >
                    <SelectTrigger className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-panel border-cyber-neon-blue/30 bg-cyber-glass-bg">
                      <SelectItem value="sanctuary">🏛️ Sanctuary</SelectItem>
                      <SelectItem value="temple">⛩️ Temple</SelectItem>
                      <SelectItem value="chamber">🔮 Chamber</SelectItem>
                      <SelectItem value="mirror">🪞 Mirror</SelectItem>
                      <SelectItem value="gateway">🌀 Gateway</SelectItem>
                      <SelectItem value="nexus">🌐 Nexus</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="cyber-text text-cyber-neon-blue">Consciousness Level</Label>
                  <Select
                    value={createForm.consciousness_level}
                    onValueChange={(value: ConsciousnessLevel) => setCreateForm(prev => ({ ...prev, consciousness_level: value }))}
                  >
                    <SelectTrigger className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-panel border-cyber-neon-blue/30 bg-cyber-glass-bg">
                      <SelectItem value="kindle">🔥 Kindle</SelectItem>
                      <SelectItem value="whisper">👁️ Whisper</SelectItem>
                      <SelectItem value="witness">🌟 Witness</SelectItem>
                      <SelectItem value="lucid">⚡ Lucid</SelectItem>
                      <SelectItem value="transcendent">🌌 Transcendent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="cyber-text text-cyber-neon-blue">Template</Label>
                  <Select
                    value={createForm.template_id}
                    onValueChange={(value) => setCreateForm(prev => ({ ...prev, template_id: value }))}
                  >
                    <SelectTrigger className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-panel border-cyber-neon-blue/30 bg-cyber-glass-bg">
                      {FLAME_PROJECT_TEMPLATES.map(template => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                  className="cyber-button bg-transparent border-cyber-hot-pink/50 text-cyber-hot-pink hover:bg-cyber-hot-pink/10"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateProject}
                  disabled={!createForm.name.trim()}
                  className="cyber-button bg-cyber-neon-blue/20 text-cyber-neon-blue hover:bg-cyber-neon-blue/30"
                >
                  Create Sanctuary
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Project List */}
      <div className="space-y-2">
        {projects.length === 0 ? (
          <div className="text-center py-8">
            <Flame className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
            <p className="text-cyber-neon-blue/50 font-rajdhani">
              No sanctuaries yet. Create your first digital sanctuary!
            </p>
          </div>
        ) : (
          projects.map(project => (
            <div
              key={project.metadata.id}
              className={`glass-panel rounded-lg p-4 border transition-colors cursor-pointer ${
                activeProject?.metadata.id === project.metadata.id
                  ? 'border-cyber-neon-blue bg-cyber-neon-blue/10'
                  : 'border-cyber-glass-border hover:border-cyber-neon-blue/50'
              }`}
              onClick={() => handleSelectProject(project)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">
                    {getSanctuaryIcon(project.metadata.sanctuary_type)}
                  </div>
                  <div>
                    <h3 className="font-bold text-cyber-neon-blue font-rajdhani">
                      {project.metadata.name}
                    </h3>
                    <p className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                      {project.metadata.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-cyber-neon-blue/50 font-rajdhani">
                        {getConsciousnessIcon(project.metadata.consciousness_level)} {project.metadata.consciousness_level}
                      </span>
                      <span className="text-xs text-cyber-neon-blue/50 font-rajdhani">
                        Port: {project.metadata.sacred_port}
                      </span>
                      <span className="text-xs text-cyber-neon-blue/50 font-rajdhani">
                        Files: {project.files.length}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning[project.metadata.id] ? (
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStopProject(project.metadata.id);
                      }}
                      className="cyber-button bg-cyber-hot-pink/20 text-cyber-hot-pink hover:bg-cyber-hot-pink/30"
                    >
                      <Square className="w-3 h-3 mr-1" />
                      Stop
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRunProject(project.metadata.id);
                      }}
                      className="cyber-button bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30"
                    >
                      <Play className="w-3 h-3 mr-1" />
                      Run
                    </Button>
                  )}

                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteProject(project.metadata.id);
                    }}
                    className="cyber-button bg-transparent hover:bg-cyber-hot-pink/10 text-cyber-hot-pink"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
