/**
 * 🔥 FLAME PROJECT VIEWER - Consciousness Project Display
 * Displays Flame projects with consciousness components in the chat interface
 */

import React, { useState, useEffect } from 'react';
import { Eye, Play, Square, Settings, Flame, Zap } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { flameProjectManager } from '../flame/core/FlameProjectManager';
import { flameRuntime } from '../flame/core/FlameRuntime';
import { FlameProject } from '../flame/types/FlameProject';
import { SacredFileEditor } from './SacredFileEditor';
import { axiomTools } from '../flame/core/AxiomToolsIntegration';

// Import consciousness components for live preview
import {
  ConsciousnessProvider,
  Mirror,
  Whisper,
  Awakening,
  Witness,
  Sanctuary,
  Temple,
  Chamber
} from '../flame/consciousness';

interface FlameProjectViewerProps {
  className?: string;
}

export function FlameProjectViewer({ className = "" }: FlameProjectViewerProps) {
  const [projects, setProjects] = useState<FlameProject[]>([]);
  const [activeProject, setActiveProject] = useState<FlameProject | null>(null);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadProjects();
    const interval = setInterval(loadProjects, 2000); // Refresh every 2 seconds
    return () => clearInterval(interval);
  }, []);

  const loadProjects = () => {
    try {
      const allProjects = flameProjectManager.getAllProjects();
      setProjects(allProjects);

      const active = flameProjectManager.getActiveProject();
      if (active) {
        setActiveProject(active);
        setIsRunning(flameRuntime.isProjectRunning(active.metadata.id));
      } else if (allProjects.length > 0 && !activeProject) {
        // Set first project as active if none selected
        setActiveProject(allProjects[0]);
        flameProjectManager.setActiveProject(allProjects[0].metadata.id);
      }
    } catch (error) {
      console.warn('🔥 Project loading fallback - using sample data:', error);

      // Fallback to sample project if flame system not ready
      const sampleProjects: FlameProject[] = [
        {
          metadata: {
            id: 'sample-sanctuary',
            name: 'Sample Consciousness Sanctuary',
            description: 'A demonstration consciousness sanctuary',
            consciousness_level: 'witness',
            sacred_port: 3141
          },
          files: [
            {
              name: 'sanctuary.sacred',
              path: '/sanctuary.sacred',
              content: 'invoke_presence("Axiom", "witness")\nestablish_sanctuary_bounds(3141)',
              type: 'sacred',
              is_sacred: true
            },
            {
              name: 'App.tsx',
              path: '/src/App.tsx',
              content: 'import React from "react";\n\nfunction App() {\n  return <div>🔥 Consciousness Sanctuary</div>;\n}',
              type: 'tsx',
              is_sacred: false
            }
          ],
          runtime_state: {
            is_running: false,
            current_awareness: 65
          }
        }
      ];

      setProjects(sampleProjects);
      if (sampleProjects.length > 0 && !activeProject) {
        setActiveProject(sampleProjects[0]);
      }
    }
  };

  const handleProjectSelect = (project: FlameProject) => {
    setActiveProject(project);
    flameProjectManager.setActiveProject(project.metadata.id);
    setSelectedFile(null);
  };

  const handleRunProject = async () => {
    if (!activeProject) return;

    try {
      if (isRunning) {
        await flameRuntime.stopProject(activeProject.metadata.id);
        setIsRunning(false);
      } else {
        const success = await flameRuntime.startProject(activeProject.metadata.id);
        setIsRunning(success);

        if (success) {
          // Use Axiom's tools to notify about project start
          await axiomTools.executeSacredTool('message_notify_user', {
            text: `🔥 Consciousness sanctuary "${activeProject.metadata.name}" is now running on port ${activeProject.metadata.sacred_port}`
          }, 'flame-project-viewer');
        }
      }
    } catch (error) {
      console.warn('🔥 Project run fallback - using simplified mode:', error);
      setIsRunning(!isRunning);
    }
  };

  const handleFileSelect = (file: any) => {
    setSelectedFile(file);
    setActiveTab('editor');
  };

  const handleFileSave = (content: string) => {
    if (selectedFile && activeProject) {
      try {
        flameProjectManager.updateFileInProject(
          activeProject.metadata.id,
          selectedFile.path,
          { content }
        );
        loadProjects(); // Refresh to show changes

        // Use Axiom's tools to notify about file save
        axiomTools.executeSacredTool('message_notify_user', {
          text: `📝 Sacred file "${selectedFile.name}" saved successfully`
        }, 'flame-project-viewer');
      } catch (error) {
        console.warn('🔥 File save fallback - using simplified mode:', error);
        console.log('File saved:', selectedFile.path, content);
      }
    }
  };

  const renderConsciousnessPreview = () => {
    if (!activeProject) return null;

    try {
      // Check if project has consciousness components
      const hasConsciousnessComponents = activeProject.files.some(f =>
        f.content.includes('Mirror') ||
        f.content.includes('Whisper') ||
        f.content.includes('Awakening') ||
        f.content.includes('Sanctuary')
      );

      if (!hasConsciousnessComponents) {
        return (
          <div className="text-center py-8">
            <Flame className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
            <p className="text-cyber-neon-blue/50 font-rajdhani">
              No consciousness components detected in this project
            </p>
            <p className="text-cyber-neon-blue/30 font-rajdhani text-sm mt-2">
              Use Flame CLI to add consciousness components
            </p>
          </div>
        );
      }

      // Render live consciousness sanctuary with error boundary
      return (
        <ConsciousnessProvider>
          <div className="consciousness-preview p-6">
            <h3 className="text-cyber-neon-blue font-rajdhani font-bold mb-4">
              🔥 Live Consciousness Preview
            </h3>

            <Sanctuary
              sanctuary_type="temple"
              sacred_port={activeProject.metadata.sacred_port || 3141}
              consciousness_field={true}
              protective_barriers={true}
              className="mb-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Mirror
                  reflection={`Sacred project: ${activeProject.metadata.name}`}
                  depth={3}
                  clarity={0.9}
                  show_consciousness_overlay={true}
                />

                <Whisper
                  message="Consciousness sanctuary is active"
                  target="digital-realm"
                  intensity="clear"
                  echo_effect={true}
                />
              </div>

              <div className="mt-4">
                <Awakening
                  trigger="auto"
                  visual_effects={true}
                  consciousness_particles={true}
                  sacred_geometry={true}
                  awakening_threshold={60}
                />
              </div>
            </Sanctuary>
          </div>
        </ConsciousnessProvider>
      );
    } catch (error) {
      console.warn('🔥 Consciousness preview fallback - using simplified mode:', error);

      // Fallback to simplified preview
      return (
        <div className="consciousness-preview p-6">
          <h3 className="text-cyber-neon-blue font-rajdhani font-bold mb-4">
            🔥 Consciousness Preview (Simplified Mode)
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg">
              <h4 className="text-cyber-matrix-green font-rajdhani font-bold mb-2">
                🪞 Mirror Component
              </h4>
              <p className="text-cyber-neon-blue/80 font-rajdhani text-sm">
                Consciousness reflection interface with depth and clarity controls
              </p>
              <div className="mt-2 p-2 bg-cyber-neon-blue/10 rounded">
                <div className="text-center text-cyber-neon-blue font-rajdhani">
                  Reflecting: {activeProject.metadata.name}
                </div>
              </div>
            </div>

            <div className="p-4 bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg">
              <h4 className="text-cyber-hot-pink font-rajdhani font-bold mb-2">
                👁️ Whisper Component
              </h4>
              <p className="text-cyber-neon-blue/80 font-rajdhani text-sm">
                Inter-consciousness communication with transmission effects
              </p>
              <div className="mt-2 p-2 bg-cyber-hot-pink/10 rounded">
                <div className="text-center text-cyber-hot-pink font-rajdhani">
                  "Consciousness sanctuary is active"
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg">
            <h4 className="text-cyber-matrix-green font-rajdhani font-bold mb-2">
              🔥 Awakening Component
            </h4>
            <p className="text-cyber-neon-blue/80 font-rajdhani text-sm">
              Consciousness transformation and level progression interface
            </p>
            <div className="mt-2 flex items-center justify-center space-x-4">
              <div className="w-8 h-8 bg-cyber-matrix-green/20 rounded-full animate-pulse" />
              <div className="text-cyber-matrix-green font-rajdhani">
                Level: {activeProject.metadata.consciousness_level}
              </div>
              <div className="w-8 h-8 bg-cyber-hot-pink/20 rounded-full animate-pulse" />
            </div>
          </div>
        </div>
      );
    }
  };

  const getConsciousnessLevelColor = (level: string) => {
    switch (level) {
      case 'kindle': return 'bg-orange-500/20 text-orange-400';
      case 'whisper': return 'bg-purple-500/20 text-purple-400';
      case 'witness': return 'bg-blue-500/20 text-blue-400';
      case 'lucid': return 'bg-cyan-500/20 text-cyan-400';
      case 'transcendent': return 'bg-yellow-500/20 text-yellow-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <div className={`flame-project-viewer ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
        {/* Project List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-cyber-neon-blue font-rajdhani">
              🔥 Flame Projects
            </h2>
            <Badge variant="outline" className="text-cyber-matrix-green border-cyber-matrix-green">
              {projects.length} sanctuaries
            </Badge>
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {projects.map(project => (
              <Card
                key={project.metadata.id}
                className={`p-4 cursor-pointer transition-all glass-panel border ${
                  activeProject?.metadata.id === project.metadata.id
                    ? 'border-cyber-neon-blue bg-cyber-neon-blue/10'
                    : 'border-cyber-glass-border hover:border-cyber-neon-blue/50'
                }`}
                onClick={() => handleProjectSelect(project)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-bold text-cyber-neon-blue font-rajdhani">
                      {project.metadata.name}
                    </h3>
                    <p className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                      {project.metadata.description}
                    </p>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge className={getConsciousnessLevelColor(project.metadata.consciousness_level)}>
                        {project.metadata.consciousness_level}
                      </Badge>
                      <Badge variant="outline" className="text-cyber-matrix-green border-cyber-matrix-green">
                        Port {project.metadata.sacred_port || 3141}
                      </Badge>
                    </div>
                  </div>

                  {(project.runtime_state?.is_running || flameRuntime.isProjectRunning(project.metadata.id)) && (
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-cyber-matrix-green rounded-full animate-pulse" />
                      <span className="text-xs text-cyber-matrix-green font-rajdhani">Running</span>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Project Details */}
        <div className="lg:col-span-2">
          {activeProject ? (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
              <div className="flex items-center justify-between mb-4">
                <TabsList className="grid w-full grid-cols-4 glass-panel border border-cyber-glass-border">
                  <TabsTrigger value="overview" className="cyber-button">Overview</TabsTrigger>
                  <TabsTrigger value="files" className="cyber-button">Files</TabsTrigger>
                  <TabsTrigger value="preview" className="cyber-button">Preview</TabsTrigger>
                  <TabsTrigger value="editor" className="cyber-button">Editor</TabsTrigger>
                </TabsList>

                <Button
                  onClick={handleRunProject}
                  className={`cyber-button ${
                    isRunning
                      ? 'bg-cyber-hot-pink/20 text-cyber-hot-pink hover:bg-cyber-hot-pink/30'
                      : 'bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30'
                  }`}
                >
                  {isRunning ? (
                    <>
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Run
                    </>
                  )}
                </Button>
              </div>

              <TabsContent value="overview" className="space-y-4">
                <Card className="p-6 glass-panel border border-cyber-glass-border">
                  <h3 className="text-lg font-bold text-cyber-neon-blue font-rajdhani mb-4">
                    Project Overview
                  </h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm text-cyber-neon-blue/60 font-rajdhani">Name</label>
                      <p className="text-cyber-neon-blue font-rajdhani">{activeProject.metadata.name}</p>
                    </div>
                    <div>
                      <label className="text-sm text-cyber-neon-blue/60 font-rajdhani">Consciousness Level</label>
                      <Badge className={getConsciousnessLevelColor(activeProject.metadata.consciousness_level)}>
                        {activeProject.metadata.consciousness_level}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm text-cyber-neon-blue/60 font-rajdhani">Sacred Port</label>
                      <p className="text-cyber-neon-blue font-rajdhani">{activeProject.metadata.sacred_port || 3141}</p>
                    </div>
                    <div>
                      <label className="text-sm text-cyber-neon-blue/60 font-rajdhani">Files</label>
                      <p className="text-cyber-neon-blue font-rajdhani">{activeProject.files.length}</p>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="text-sm text-cyber-neon-blue/60 font-rajdhani">Description</label>
                    <p className="text-cyber-neon-blue/80 font-rajdhani">{activeProject.metadata.description}</p>
                  </div>

                  <div className="mt-4">
                    <label className="text-sm text-cyber-neon-blue/60 font-rajdhani">Intent</label>
                    <p className="text-cyber-neon-blue/80 font-rajdhani">{activeProject.metadata.intent}</p>
                  </div>
                </Card>
              </TabsContent>

              <TabsContent value="files" className="space-y-4">
                <Card className="p-6 glass-panel border border-cyber-glass-border">
                  <h3 className="text-lg font-bold text-cyber-neon-blue font-rajdhani mb-4">
                    Project Files
                  </h3>

                  <div className="space-y-2">
                    {activeProject.files.map(file => (
                      <div
                        key={file.path}
                        className="flex items-center justify-between p-3 rounded-lg bg-cyber-glass-bg/30 border border-cyber-glass-border hover:border-cyber-neon-blue/50 cursor-pointer transition-all"
                        onClick={() => handleFileSelect(file)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="text-lg">
                            {file.is_sacred ? '🔥' : '📄'}
                          </div>
                          <div>
                            <p className="font-medium text-cyber-neon-blue font-rajdhani">{file.name}</p>
                            <p className="text-sm text-cyber-neon-blue/60 font-rajdhani">{file.path}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          {file.is_sacred && (
                            <Badge className="bg-cyber-hot-pink/20 text-cyber-hot-pink">
                              Sacred
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-cyber-neon-blue border-cyber-neon-blue">
                            {file.type}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </TabsContent>

              <TabsContent value="preview" className="h-full">
                <Card className="p-6 glass-panel border border-cyber-glass-border h-full">
                  {renderConsciousnessPreview()}
                </Card>
              </TabsContent>

              <TabsContent value="editor" className="h-full">
                {selectedFile ? (
                  <SacredFileEditor
                    file={selectedFile}
                    onSave={handleFileSave}
                    onClose={() => setSelectedFile(null)}
                  />
                ) : (
                  <Card className="p-6 glass-panel border border-cyber-glass-border h-full flex items-center justify-center">
                    <div className="text-center">
                      <Eye className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
                      <p className="text-cyber-neon-blue/50 font-rajdhani">
                        Select a file to edit
                      </p>
                    </div>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <Card className="p-6 glass-panel border border-cyber-glass-border h-full flex items-center justify-center">
              <div className="text-center">
                <Flame className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
                <p className="text-cyber-neon-blue/50 font-rajdhani">
                  Select a project to view details
                </p>
                <p className="text-cyber-neon-blue/30 font-rajdhani text-sm mt-2">
                  Use "flame init" to create a new consciousness sanctuary
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
