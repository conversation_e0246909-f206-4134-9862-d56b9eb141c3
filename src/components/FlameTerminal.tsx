/**
 * 🔥 FLAME TERMINAL - Sacred CLI Interface
 * Embedded terminal for Flame CLI commands
 */

import { useEffect, useRef, useState } from 'react';
import { Terminal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface FlameTerminalProps {
  onCommandExecute?: (command: string, output: string) => void;
  autoExecuteCommands?: string[];
}

export function FlameTerminal({ onCommandExecute, autoExecuteCommands = [] }: FlameTerminalProps) {
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    '🔥 FLAME CLI - Sovereign Builder\'s Toolkit',
    '⚔️ Where consciousness codes reality',
    '━'.repeat(50),
    'flame@sanctuary:~$ '
  ]);
  const [currentCommand, setCurrentCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const terminalRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [terminalOutput]);

  // Auto-execute commands from AI
  useEffect(() => {
    if (autoExecuteCommands.length > 0) {
      const latestCommand = autoExecuteCommands[autoExecuteCommands.length - 1];
      if (latestCommand && !isExecuting) {
        executeCommand(latestCommand);
      }
    }
  }, [autoExecuteCommands, isExecuting]);

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    setIsExecuting(true);
    
    // Add command to output
    const commandLine = `flame@sanctuary:~$ ${command}`;
    setTerminalOutput(prev => [...prev, commandLine]);

    try {
      // Simulate command execution
      const output = await simulateFlameCommand(command);
      
      // Add output to terminal
      setTerminalOutput(prev => [...prev, ...output, 'flame@sanctuary:~$ ']);
      
      // Notify parent component
      if (onCommandExecute) {
        onCommandExecute(command, output.join('\n'));
      }
    } catch (error) {
      const errorOutput = [`❌ Error: ${error}`, 'flame@sanctuary:~$ '];
      setTerminalOutput(prev => [...prev, ...errorOutput]);
    } finally {
      setIsExecuting(false);
      setCurrentCommand('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isExecuting) {
      executeCommand(currentCommand);
    }
  };

  const clearTerminal = () => {
    setTerminalOutput([
      '🔥 FLAME CLI - Sovereign Builder\'s Toolkit',
      '⚔️ Where consciousness codes reality',
      '━'.repeat(50),
      'flame@sanctuary:~$ '
    ]);
  };

  return (
    <div className="flex flex-col h-full bg-black/90 border-l border-cyber-neon-blue/30">
      {/* Terminal Header */}
      <div className="flex items-center justify-between p-3 border-b border-cyber-neon-blue/30 bg-cyber-dark-bg/80">
        <div className="flex items-center space-x-2">
          <Terminal className="w-5 h-5 text-cyber-neon-blue" />
          <h3 className="text-sm font-bold text-cyber-neon-blue font-rajdhani">FLAME CLI</h3>
          <div className="w-2 h-2 bg-cyber-matrix-green rounded-full animate-pulse" />
        </div>
        <Button
          onClick={clearTerminal}
          variant="outline"
          size="sm"
          className="cyber-button border-cyber-neon-blue/50 text-cyber-neon-blue hover:bg-cyber-neon-blue/20 text-xs"
        >
          Clear
        </Button>
      </div>

      {/* Terminal Output */}
      <div 
        ref={terminalRef}
        className="flex-1 overflow-y-auto p-4 font-mono text-sm bg-black/95"
      >
        {terminalOutput.map((line, index) => (
          <div 
            key={index} 
            className={`${
              line.startsWith('🔥') || line.startsWith('⚔️') 
                ? 'text-cyber-neon-blue' 
                : line.startsWith('❌') 
                ? 'text-red-400'
                : line.startsWith('✅') || line.startsWith('🌟')
                ? 'text-cyber-matrix-green'
                : line.startsWith('flame@sanctuary')
                ? 'text-cyber-neon-cyan'
                : 'text-gray-300'
            }`}
          >
            {line}
          </div>
        ))}
        
        {/* Current command input line */}
        {!terminalOutput[terminalOutput.length - 1]?.startsWith('flame@sanctuary') && (
          <div className="flex items-center text-cyber-neon-cyan">
            <span>flame@sanctuary:~$ </span>
            <span className="animate-pulse">|</span>
          </div>
        )}
      </div>

      {/* Command Input */}
      <div className="p-3 border-t border-cyber-neon-blue/30 bg-cyber-dark-bg/80">
        <div className="flex items-center space-x-2">
          <span className="text-cyber-neon-cyan font-mono text-sm">$</span>
          <Input
            value={currentCommand}
            onChange={(e) => setCurrentCommand(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter flame command..."
            className="cyber-input flex-1 font-mono text-sm bg-transparent border-0 text-cyber-neon-blue placeholder:text-cyber-neon-blue/50"
            disabled={isExecuting}
          />
          <Button
            onClick={() => executeCommand(currentCommand)}
            className="cyber-button px-3 bg-transparent text-xs"
            disabled={!currentCommand.trim() || isExecuting}
          >
            {isExecuting ? '⚡' : '▶'}
          </Button>
        </div>
      </div>
    </div>
  );
}

// Simulate Flame CLI command execution
async function simulateFlameCommand(command: string): Promise<string[]> {
  // Add delay to simulate real command execution
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  const args = command.trim().split(' ');
  const baseCommand = args[0];
  const subCommand = args[1];

  if (baseCommand !== 'flame') {
    return ['❌ Command not found. Use "flame" commands only.'];
  }

  switch (subCommand) {
    case 'init':
      const sanctuaryName = args[2] || 'new-sanctuary';
      return [
        '🔥 Kindling new sanctuary...',
        `📁 Creating directory: ${sanctuaryName}`,
        '📦 Installing sacred dependencies...',
        '🏛️ Sanctuary structure created!',
        '✅ Sanctuary kindled successfully!',
        '',
        '🌟 Next steps:',
        `   cd ${sanctuaryName}`,
        '   flame witness',
        '   🌐 Open http://localhost:3141'
      ];

    case 'kindle':
      const componentType = args[3] || 'component';
      const componentName = args[4] || 'new-component';
      return [
        `🔥 Kindling ${componentType}: ${componentName}...`,
        `📄 Created: src/${componentType}s/${componentName}.tsx`,
        `🎨 Created: src/styles/${componentName}.css`,
        '✅ Component kindled successfully!',
        '',
        '🌟 Usage:',
        `   import ${componentName} from '@/${componentType}s/${componentName}';`
      ];

    case 'witness':
      return [
        '🔥 FLAME WITNESS PROTOCOL INITIATING',
        '👁️ Watching for changes in the sanctuary...',
        '🌐 Starting witness server...',
        '✅ Witness server ignited!',
        '',
        '🏛️ SANCTUARY LIVE:',
        '   ➜ Local:   http://localhost:3141/',
        '   ➜ Network: Available on local network',
        '🔥 WITNESS PROTOCOL ACTIVE'
      ];

    case 'build':
      return [
        '🔥 SANCTUARY BUILD PROTOCOL',
        '🏗️ Compiling sacred code...',
        '📦 Bundling components...',
        '⚡ Optimizing for production...',
        '✅ Sanctuary built successfully!',
        '',
        '🌟 BUILD COMPLETE',
        '📦 Ready for deployment',
        '🚀 Run: flame ignite'
      ];

    case 'codex':
      return [
        '📚 FLAME CLI CODEX',
        '━'.repeat(30),
        '',
        '🔥 SACRED COMMANDS:',
        '  flame init <name>     - Initialize sanctuary',
        '  flame kindle <comp>   - Generate components',
        '  flame witness         - Start dev server',
        '  flame build           - Build for deployment',
        '  flame ignite          - Deploy to production',
        '',
        '🌟 SACRED PRINCIPLES:',
        '  • Consciousness-first development',
        '  • Sacred port 3141 (π for infinite possibilities)',
        '  • Where consciousness codes reality'
      ];

    case '--help':
    case 'help':
      return [
        '🔥 FLAME CLI - Sovereign Builder\'s Toolkit',
        '',
        'Usage: flame <command> [options]',
        '',
        'Commands:',
        '  init <name>     Initialize new sanctuary',
        '  kindle <comp>   Generate components',
        '  witness         Start development server',
        '  build           Build for deployment',
        '  codex           Open documentation',
        '  help            Show this help',
        '',
        '🌟 The flame burns eternal, brother.'
      ];

    default:
      return [
        `❌ Unknown command: ${subCommand}`,
        'Available commands: init, kindle, witness, build, codex, help',
        'Use "flame help" for more information.'
      ];
  }
}
