/**
 * 🔥 FLAME TEST RUNNER - Consciousness System Testing Interface
 * Interactive test runner for validating the complete Flame consciousness system
 */

import React, { useState, useEffect } from 'react';
import { Play, Square, CheckCircle, XCircle, Clock, Zap, Activity } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { runFlameIntegrationTests, TestResult } from '../tests/FlameIntegrationTests';
import { flameErrorHandler } from '../flame/core/FlameErrorHandler';
import { flamePerformanceOptimizer } from '../flame/core/FlamePerformanceOptimizer';

interface FlameTestRunnerProps {
  className?: string;
}

export function FlameTestRunner({ className = "" }: FlameTestRunnerProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [totalConsciousnessImpact, setTotalConsciousnessImpact] = useState(0);
  const [testStartTime, setTestStartTime] = useState<Date | null>(null);
  const [testEndTime, setTestEndTime] = useState<Date | null>(null);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setProgress(0);
    setTotalConsciousnessImpact(0);
    setTestStartTime(new Date());
    setTestEndTime(null);

    try {
      console.log('🔥 Starting Flame Integration Tests...');
      
      // Mock progress updates (in real implementation, this would come from the test runner)
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 2, 95));
      }, 100);

      const results = await runFlameIntegrationTests();
      
      clearInterval(progressInterval);
      setProgress(100);
      setTestResults(results);
      
      const totalImpact = results.reduce((sum, r) => sum + (r.consciousness_impact || 0), 0);
      setTotalConsciousnessImpact(totalImpact);
      
      setTestEndTime(new Date());
      console.log('✅ Flame Integration Tests Complete!');
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      setTestResults([{
        name: 'Test Execution',
        passed: false,
        message: `Test execution failed: ${error}`,
        duration: 0
      }]);
      setTestEndTime(new Date());
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const getTestStats = () => {
    const total = testResults.length;
    const passed = testResults.filter(r => r.passed).length;
    const failed = total - passed;
    const successRate = total > 0 ? (passed / total) * 100 : 0;
    const totalDuration = testResults.reduce((sum, r) => sum + r.duration, 0);

    return { total, passed, failed, successRate, totalDuration };
  };

  const getTestIcon = (result: TestResult) => {
    if (result.passed) {
      return <CheckCircle className="w-4 h-4 text-cyber-matrix-green" />;
    } else {
      return <XCircle className="w-4 h-4 text-cyber-hot-pink" />;
    }
  };

  const getTestStatusColor = (result: TestResult) => {
    return result.passed 
      ? 'text-cyber-matrix-green' 
      : 'text-cyber-hot-pink';
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const stats = getTestStats();

  return (
    <div className={`flame-test-runner ${className}`}>
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Activity className="w-8 h-8 text-cyber-matrix-green" />
            <div>
              <h1 className="text-2xl font-bold text-cyber-neon-blue font-rajdhani">
                Flame Test Runner
              </h1>
              <p className="text-cyber-neon-blue/60 font-rajdhani">
                Consciousness system validation and testing
              </p>
            </div>
          </div>

          <Button
            onClick={runTests}
            disabled={isRunning}
            className={`cyber-button ${
              isRunning 
                ? 'bg-cyber-hot-pink/20 text-cyber-hot-pink' 
                : 'bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30'
            }`}
          >
            {isRunning ? (
              <>
                <Square className="w-4 h-4 mr-2" />
                Running Tests...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Run Tests
              </>
            )}
          </Button>
        </div>

        {/* Progress Bar */}
        {isRunning && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-cyber-neon-blue/80 font-rajdhani">
                {currentTest || 'Initializing tests...'}
              </span>
              <span className="text-cyber-matrix-green font-rajdhani">
                {progress.toFixed(0)}%
              </span>
            </div>
            <Progress 
              value={progress} 
              className="h-2 bg-cyber-glass-bg/30 border border-cyber-glass-border"
            />
          </div>
        )}
      </div>

      <Tabs defaultValue="results" className="w-full">
        <TabsList className="grid w-full grid-cols-4 glass-panel border border-cyber-glass-border">
          <TabsTrigger value="results" className="cyber-button">Test Results</TabsTrigger>
          <TabsTrigger value="performance" className="cyber-button">Performance</TabsTrigger>
          <TabsTrigger value="errors" className="cyber-button">Error Log</TabsTrigger>
          <TabsTrigger value="consciousness" className="cyber-button">Consciousness</TabsTrigger>
        </TabsList>

        <TabsContent value="results" className="space-y-6">
          {/* Test Summary */}
          <Card className="p-6 glass-panel border border-cyber-glass-border">
            <h2 className="text-xl font-bold text-cyber-neon-blue font-rajdhani mb-4">
              Test Summary
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-cyber-neon-blue font-rajdhani">
                  {stats.total}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Total Tests</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-cyber-matrix-green font-rajdhani">
                  {stats.passed}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Passed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-cyber-hot-pink font-rajdhani">
                  {stats.failed}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Failed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-cyber-neon-blue font-rajdhani">
                  {stats.successRate.toFixed(1)}%
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Success Rate</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-cyber-neon-blue font-rajdhani">
                  {formatDuration(stats.totalDuration)}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Duration</div>
              </div>
            </div>

            {testStartTime && testEndTime && (
              <div className="mt-4 pt-4 border-t border-cyber-glass-border">
                <div className="flex items-center justify-between text-sm text-cyber-neon-blue/60 font-rajdhani">
                  <span>Started: {testStartTime.toLocaleTimeString()}</span>
                  <span>Completed: {testEndTime.toLocaleTimeString()}</span>
                </div>
              </div>
            )}
          </Card>

          {/* Test Results List */}
          <Card className="p-6 glass-panel border border-cyber-glass-border">
            <h3 className="text-lg font-bold text-cyber-neon-blue font-rajdhani mb-4">
              Detailed Results
            </h3>
            
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.length > 0 ? (
                testResults.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-lg bg-cyber-glass-bg/30 border border-cyber-glass-border"
                  >
                    <div className="flex items-center space-x-3">
                      {getTestIcon(result)}
                      <div>
                        <p className={`font-medium font-rajdhani ${getTestStatusColor(result)}`}>
                          {result.name}
                        </p>
                        {!result.passed && (
                          <p className="text-sm text-cyber-hot-pink/60 font-rajdhani">
                            {result.message}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-cyber-neon-blue border-cyber-neon-blue">
                        {formatDuration(result.duration)}
                      </Badge>
                      {result.consciousness_impact && (
                        <Badge className="bg-cyber-hot-pink/20 text-cyber-hot-pink">
                          +{result.consciousness_impact}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
                  <p className="text-cyber-neon-blue/50 font-rajdhani">
                    No test results yet. Click "Run Tests" to start validation.
                  </p>
                </div>
              )}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card className="p-6 glass-panel border border-cyber-glass-border">
            <h3 className="text-lg font-bold text-cyber-neon-blue font-rajdhani mb-4">
              Performance Metrics
            </h3>
            
            <div className="space-y-4">
              <div className="bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg p-4">
                <pre className="text-cyber-matrix-green font-mono text-sm whitespace-pre-wrap">
                  {flamePerformanceOptimizer.getPerformanceReport()}
                </pre>
              </div>
              
              <div className="flex items-center space-x-4">
                <Button
                  onClick={() => flamePerformanceOptimizer.clearCache()}
                  size="sm"
                  className="cyber-button bg-cyber-hot-pink/20 text-cyber-hot-pink hover:bg-cyber-hot-pink/30"
                >
                  Clear Cache
                </Button>
                <span className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                  Clear performance cache and metrics
                </span>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-6">
          <Card className="p-6 glass-panel border border-cyber-glass-border">
            <h3 className="text-lg font-bold text-cyber-neon-blue font-rajdhani mb-4">
              Error History
            </h3>
            
            <div className="space-y-4">
              <div className="bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg p-4">
                <pre className="text-cyber-hot-pink font-mono text-sm whitespace-pre-wrap">
                  {flameErrorHandler.generateErrorReport()}
                </pre>
              </div>
              
              <div className="flex items-center space-x-4">
                <Button
                  onClick={() => flameErrorHandler.clearErrorHistory()}
                  size="sm"
                  className="cyber-button bg-cyber-hot-pink/20 text-cyber-hot-pink hover:bg-cyber-hot-pink/30"
                >
                  Clear History
                </Button>
                <span className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                  Clear error history and logs
                </span>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="consciousness" className="space-y-6">
          <Card className="p-6 glass-panel border border-cyber-glass-border">
            <h3 className="text-lg font-bold text-cyber-neon-blue font-rajdhani mb-4">
              Consciousness Impact
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg">
                <div className="text-2xl font-bold text-cyber-hot-pink font-rajdhani">
                  {totalConsciousnessImpact}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                  Total Consciousness Impact
                </div>
              </div>
              
              <div className="text-center p-4 bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg">
                <div className="text-2xl font-bold text-cyber-matrix-green font-rajdhani">
                  {testResults.filter(r => r.consciousness_impact && r.consciousness_impact > 0).length}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                  Consciousness-Enhancing Tests
                </div>
              </div>
              
              <div className="text-center p-4 bg-cyber-glass-bg/30 border border-cyber-glass-border rounded-lg">
                <div className="text-2xl font-bold text-cyber-neon-blue font-rajdhani">
                  {testResults.length > 0 ? (totalConsciousnessImpact / testResults.length).toFixed(1) : '0'}
                </div>
                <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                  Average Impact per Test
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="font-bold text-cyber-matrix-green font-rajdhani mb-2">
                Consciousness Test Breakdown
              </h4>
              <div className="space-y-2">
                {testResults
                  .filter(r => r.consciousness_impact && r.consciousness_impact > 0)
                  .map((result, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-cyber-glass-bg/20 rounded"
                    >
                      <span className="text-cyber-neon-blue font-rajdhani text-sm">
                        {result.name}
                      </span>
                      <div className="flex items-center space-x-2">
                        <Zap className="w-3 h-3 text-cyber-hot-pink" />
                        <span className="text-cyber-hot-pink font-rajdhani text-sm">
                          +{result.consciousness_impact}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
