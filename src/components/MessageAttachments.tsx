/**
 * 🔥 MESSAGE ATTACHMENTS DISPLAY - File Preview Component
 * Shows attached files in chat messages with previews and download options
 */

import React, { useState } from 'react';
import { FileText, Image, Code, File, Download, Eye, X } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AttachedFile } from '@/utils/chatStorage';

interface MessageAttachmentsProps {
  attachments: AttachedFile[];
  className?: string;
}

const getFileIcon = (type: string) => {
  switch (type) {
    case 'image': return Image;
    case 'text': return FileText;
    case 'code': return Code;
    default: return File;
  }
};

const getFileColor = (type: string) => {
  switch (type) {
    case 'image': return 'text-cyber-neon-blue';
    case 'text': return 'text-cyber-matrix-green';
    case 'code': return 'text-cyber-neon-purple';
    default: return 'text-cyber-neon-orange';
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export function MessageAttachments({ attachments, className = "" }: MessageAttachmentsProps) {
  const [previewFile, setPreviewFile] = useState<AttachedFile | null>(null);

  if (!attachments || attachments.length === 0) {
    return null;
  }

  const handleDownload = (attachment: AttachedFile) => {
    try {
      let blob: Blob;
      let url: string;

      if (attachment.type === 'image' && attachment.url) {
        // For images, convert base64 to blob if needed
        if (attachment.url.startsWith('data:')) {
          const byteCharacters = atob(attachment.url.split(',')[1]);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          blob = new Blob([byteArray], { type: 'image/png' });
        } else {
          // If it's already a blob URL, fetch it
          fetch(attachment.url)
            .then(response => response.blob())
            .then(blob => {
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = attachment.name;
              a.click();
              URL.revokeObjectURL(url);
            });
          return;
        }
      } else if (attachment.content) {
        // For text/code files
        blob = new Blob([attachment.content], { type: 'text/plain' });
      } else {
        console.error('No content available for download');
        return;
      }

      url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = attachment.name;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const handlePreview = (attachment: AttachedFile) => {
    setPreviewFile(attachment);
  };

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <div className="text-xs text-cyber-neon-blue/60 font-rajdhani font-medium">
          📎 Attachments ({attachments.length})
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {attachments.map((attachment) => {
            const IconComponent = getFileIcon(attachment.type);
            const colorClass = getFileColor(attachment.type);
            
            return (
              <div
                key={attachment.id}
                className="glass-panel rounded-lg p-3 border border-cyber-glass-border hover:border-cyber-neon-blue/30 transition-colors"
              >
                <div className="flex items-start space-x-3">
                  {attachment.type === 'image' && attachment.url ? (
                    <img
                      src={attachment.url}
                      alt={attachment.name}
                      className="w-12 h-12 rounded object-cover cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => handlePreview(attachment)}
                    />
                  ) : (
                    <div className="w-12 h-12 glass-panel rounded flex items-center justify-center">
                      <IconComponent className={`w-6 h-6 ${colorClass}`} />
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-cyber-neon-blue truncate font-rajdhani">
                      {attachment.name}
                    </div>
                    <div className="text-xs text-cyber-neon-blue/60 font-rajdhani">
                      {formatFileSize(attachment.size)}
                    </div>
                    
                    <div className="flex space-x-1 mt-2">
                      {(attachment.content || attachment.url) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(attachment)}
                          className="h-6 px-2 text-xs cyber-button bg-transparent hover:bg-cyber-neon-blue/10"
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          Preview
                        </Button>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownload(attachment)}
                        className="h-6 px-2 text-xs cyber-button bg-transparent hover:bg-cyber-matrix-green/10"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Preview Dialog */}
      {previewFile && (
        <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
          <DialogContent className="glass-panel border-2 border-cyber-neon-blue/30 bg-cyber-glass-bg max-w-4xl max-h-[80vh] overflow-auto">
            <DialogHeader>
              <DialogTitle className="cyber-text text-cyber-neon-blue flex items-center space-x-2">
                <span>📎 {previewFile.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPreviewFile(null)}
                  className="ml-auto cyber-button bg-transparent hover:bg-cyber-hot-pink/10"
                >
                  <X className="w-4 h-4 text-cyber-hot-pink" />
                </Button>
              </DialogTitle>
            </DialogHeader>

            <div className="mt-4">
              {previewFile.type === 'image' && previewFile.url ? (
                <img
                  src={previewFile.url}
                  alt={previewFile.name}
                  className="max-w-full h-auto rounded-lg"
                />
              ) : previewFile.content ? (
                <div className="glass-panel rounded-lg p-4 border border-cyber-glass-border">
                  <pre className="text-sm text-cyber-neon-blue font-mono whitespace-pre-wrap overflow-auto max-h-96">
                    {previewFile.content}
                  </pre>
                </div>
              ) : (
                <div className="text-center text-cyber-neon-blue/60 font-rajdhani">
                  Preview not available for this file type
                </div>
              )}
            </div>

            <div className="flex justify-end mt-4">
              <Button
                onClick={() => handleDownload(previewFile)}
                className="cyber-button bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30"
              >
                <Download className="w-4 h-4 mr-2" />
                Download File
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
