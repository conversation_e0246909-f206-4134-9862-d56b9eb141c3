/**
 * 🔥 SACRED FILE EDITOR - Consciousness Code Editor
 * Advanced editor for sacred file types with syntax highlighting and validation
 */

import React, { useState, useEffect, useRef } from 'react';
import { AlertTriangle, CheckCircle, Info, Lightbulb, Eye, Code, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

import { FlameFile } from '../flame/types/FlameProject';
import { sacredFileHandlerFactory } from '../flame/parsers/SacredFileHandlers';
import { sacredValidationEngine } from '../flame/parsers/SacredValidationEngine';
import { sacredSyntaxHighlighter } from '../flame/parsers/SacredSyntaxHighlighter';

interface SacredFileEditorProps {
  file: FlameFile;
  onSave: (content: string) => void;
  onClose: () => void;
  className?: string;
}

export function SacredFileEditor({ file, onSave, onClose, className = "" }: SacredFileEditorProps) {
  const [content, setContent] = useState(file.content);
  const [isModified, setIsModified] = useState(false);
  const [validationReport, setValidationReport] = useState<any>(null);
  const [syntaxTokens, setSyntaxTokens] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('editor');
  const editorRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    validateContent();
    highlightSyntax();
  }, [content]);

  const validateContent = async () => {
    if (!file.is_sacred) return;

    const handler = sacredFileHandlerFactory.getHandler(file.type as any);
    if (handler) {
      try {
        const parseResult = handler.parse(content);
        const report = sacredValidationEngine.validate(parseResult, file.type as any);
        setValidationReport(report);
      } catch (error) {
        console.error('Validation error:', error);
      }
    }
  };

  const highlightSyntax = () => {
    if (!file.is_sacred) return;

    try {
      const tokens = sacredSyntaxHighlighter.tokenize(content, file.type as any);
      setSyntaxTokens(tokens);
    } catch (error) {
      console.error('Syntax highlighting error:', error);
    }
  };

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    setIsModified(newContent !== file.content);
  };

  const handleSave = () => {
    onSave(content);
    setIsModified(false);
  };

  const handleAutoFix = () => {
    if (validationReport?.auto_fixes?.length > 0) {
      const fixedContent = content + '\n\n// Auto-generated fixes:\n' + 
        validationReport.auto_fixes.join('\n');
      setContent(fixedContent);
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info': return <Info className="w-4 h-4 text-blue-500" />;
      default: return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error': return 'border-red-500 bg-red-500/10';
      case 'warning': return 'border-yellow-500 bg-yellow-500/10';
      case 'info': return 'border-blue-500 bg-blue-500/10';
      default: return 'border-gray-500 bg-gray-500/10';
    }
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'sacred': return '🔥';
      case 'flame': return '⚡';
      case 'mirror': return '🪞';
      case 'whisper': return '👁️';
      default: return '📄';
    }
  };

  return (
    <div className={`glass-panel rounded-lg border border-cyber-glass-border ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-cyber-glass-border">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getFileTypeIcon(file.type)}</span>
          <div>
            <h3 className="font-bold text-cyber-neon-blue font-rajdhani">
              {file.name}
            </h3>
            <p className="text-sm text-cyber-neon-blue/60 font-rajdhani">
              {file.path} • {file.type} file
              {isModified && <span className="text-cyber-hot-pink ml-2">• Modified</span>}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {validationReport && (
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                Score: {validationReport.consciousness_score}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {validationReport.sacred_completeness.toFixed(0)}% Complete
              </Badge>
            </div>
          )}
          
          <Button
            size="sm"
            onClick={handleSave}
            disabled={!isModified}
            className="cyber-button bg-cyber-matrix-green/20 text-cyber-matrix-green hover:bg-cyber-matrix-green/30"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={onClose}
            className="cyber-button bg-transparent border-cyber-hot-pink/50 text-cyber-hot-pink hover:bg-cyber-hot-pink/10"
          >
            Close
          </Button>
        </div>
      </div>

      {/* Editor Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
        <div className="px-4 pt-4">
          <TabsList className="grid w-full grid-cols-3 glass-panel border border-cyber-glass-border">
            <TabsTrigger 
              value="editor" 
              className="cyber-button bg-transparent data-[state=active]:bg-cyber-neon-blue/20 data-[state=active]:text-cyber-neon-blue"
            >
              <Code className="w-4 h-4 mr-2" />
              Editor
            </TabsTrigger>
            <TabsTrigger 
              value="preview"
              className="cyber-button bg-transparent data-[state=active]:bg-cyber-neon-blue/20 data-[state=active]:text-cyber-neon-blue"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </TabsTrigger>
            <TabsTrigger 
              value="validation"
              className="cyber-button bg-transparent data-[state=active]:bg-cyber-neon-blue/20 data-[state=active]:text-cyber-neon-blue"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Validation
              {validationReport?.metrics?.total_issues > 0 && (
                <Badge variant="destructive" className="ml-2 text-xs">
                  {validationReport.metrics.total_issues}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="editor" className="p-4 space-y-4">
          <div className="relative">
            <Textarea
              ref={editorRef}
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              className="cyber-input bg-transparent border-cyber-neon-blue/30 text-cyber-neon-blue font-mono text-sm min-h-[400px] resize-none"
              placeholder={`Enter your ${file.type} code here...`}
              style={{
                fontFamily: '"Fira Code", "Monaco", "Menlo", monospace',
                lineHeight: '1.5',
                tabSize: 2
              }}
            />
            
            {/* Line numbers overlay */}
            <div className="absolute left-2 top-2 pointer-events-none text-cyber-neon-blue/30 font-mono text-sm leading-6">
              {content.split('\n').map((_, index) => (
                <div key={index} className="h-6">
                  {index + 1}
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          {file.is_sacred && (
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleAutoFix}
                disabled={!validationReport?.auto_fixes?.length}
                className="cyber-button bg-transparent border-cyber-matrix-green/50 text-cyber-matrix-green hover:bg-cyber-matrix-green/10"
              >
                <Lightbulb className="w-3 h-3 mr-1" />
                Auto Fix ({validationReport?.auto_fixes?.length || 0})
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="preview" className="p-4">
          {file.is_sacred ? (
            <div 
              className="sacred-editor border border-cyber-glass-border rounded-lg p-4 bg-cyber-glass-bg/30"
              dangerouslySetInnerHTML={{
                __html: sacredSyntaxHighlighter.generateHTML(syntaxTokens)
              }}
              style={{ fontFamily: '"Fira Code", "Monaco", "Menlo", monospace' }}
            />
          ) : (
            <div className="text-center py-8">
              <Code className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
              <p className="text-cyber-neon-blue/50 font-rajdhani">
                Syntax highlighting not available for {file.type} files
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="validation" className="p-4 space-y-4">
          {validationReport ? (
            <>
              {/* Validation Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="glass-panel rounded-lg p-3 border border-cyber-glass-border">
                  <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Consciousness Score</div>
                  <div className="text-xl font-bold text-cyber-neon-blue font-rajdhani">
                    {validationReport.consciousness_score}
                  </div>
                </div>
                
                <div className="glass-panel rounded-lg p-3 border border-cyber-glass-border">
                  <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Sacred Completeness</div>
                  <div className="text-xl font-bold text-cyber-matrix-green font-rajdhani">
                    {validationReport.sacred_completeness.toFixed(0)}%
                  </div>
                </div>
                
                <div className="glass-panel rounded-lg p-3 border border-cyber-glass-border">
                  <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Issues</div>
                  <div className="text-xl font-bold text-cyber-hot-pink font-rajdhani">
                    {validationReport.metrics.total_issues}
                  </div>
                </div>
                
                <div className="glass-panel rounded-lg p-3 border border-cyber-glass-border">
                  <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">Status</div>
                  <div className={`text-xl font-bold font-rajdhani ${
                    validationReport.is_valid ? 'text-cyber-matrix-green' : 'text-cyber-hot-pink'
                  }`}>
                    {validationReport.is_valid ? 'Valid' : 'Issues'}
                  </div>
                </div>
              </div>

              {/* Issues List */}
              {validationReport.issues.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-bold text-cyber-neon-blue font-rajdhani">Issues</h4>
                  {validationReport.issues.map((issue: any, index: number) => (
                    <div
                      key={index}
                      className={`glass-panel rounded-lg p-3 border ${getSeverityColor(issue.severity)}`}
                    >
                      <div className="flex items-start space-x-3">
                        {getSeverityIcon(issue.severity)}
                        <div className="flex-1">
                          <div className="font-medium text-cyber-neon-blue font-rajdhani">
                            {issue.message}
                          </div>
                          {issue.line_number && (
                            <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">
                              Line {issue.line_number}
                            </div>
                          )}
                          {issue.suggestion && (
                            <div className="text-sm text-cyber-matrix-green font-rajdhani mt-1">
                              💡 {issue.suggestion}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Suggestions */}
              {validationReport.suggestions.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-bold text-cyber-neon-blue font-rajdhani">Suggestions</h4>
                  {validationReport.suggestions.map((suggestion: string, index: number) => (
                    <div key={index} className="glass-panel rounded-lg p-3 border border-cyber-matrix-green/30 bg-cyber-matrix-green/10">
                      <div className="flex items-start space-x-3">
                        <Lightbulb className="w-4 h-4 text-cyber-matrix-green mt-0.5" />
                        <div className="text-cyber-matrix-green font-rajdhani">
                          {suggestion}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-cyber-neon-blue/30 mx-auto mb-4" />
              <p className="text-cyber-neon-blue/50 font-rajdhani">
                Validation not available for {file.type} files
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Inject syntax highlighting CSS */}
      <style dangerouslySetInnerHTML={{
        __html: sacredSyntaxHighlighter.generateCSS()
      }} />
    </div>
  );
}
