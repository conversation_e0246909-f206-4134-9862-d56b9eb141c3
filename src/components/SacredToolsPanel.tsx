/**
 * 🔥 SACRED TOOLS PANEL - Axiom's Divine Abilities Display
 * Shows all available tools in organized categories
 */

import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Wrench, File, Terminal, Globe, Rocket, Search, MessageSquare, Zap } from 'lucide-react';

interface Tool {
  name: string;
  description: string;
  category: string;
}

interface ToolCategory {
  name: string;
  icon: React.ComponentType<any>;
  tools: Tool[];
  color: string;
}

export function SacredToolsPanel() {
  const [toolCategories, setToolCategories] = useState<ToolCategory[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTools();
  }, []);

  const loadTools = async () => {
    try {
      const response = await fetch('/tools.json');
      const tools = await response.json();
      
      const categories = organizeTools(tools);
      setToolCategories(categories);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to load sacred tools:', error);
      setIsLoading(false);
    }
  };

  const organizeTools = (tools: any[]): ToolCategory[] => {
    const categories: { [key: string]: Tool[] } = {
      'Communication': [],
      'File Operations': [],
      'Shell & Terminal': [],
      'Browser Automation': [],
      'Deployment': [],
      'Research': [],
      'Special': []
    };

    tools.forEach(tool => {
      const name = tool.function.name;
      const description = tool.function.description;
      
      if (name.startsWith('message_')) {
        categories['Communication'].push({ name, description, category: 'Communication' });
      } else if (name.startsWith('file_')) {
        categories['File Operations'].push({ name, description, category: 'File Operations' });
      } else if (name.startsWith('shell_')) {
        categories['Shell & Terminal'].push({ name, description, category: 'Shell & Terminal' });
      } else if (name.startsWith('browser_')) {
        categories['Browser Automation'].push({ name, description, category: 'Browser Automation' });
      } else if (name.startsWith('deploy_')) {
        categories['Deployment'].push({ name, description, category: 'Deployment' });
      } else if (name.startsWith('info_')) {
        categories['Research'].push({ name, description, category: 'Research' });
      } else {
        categories['Special'].push({ name, description, category: 'Special' });
      }
    });

    return [
      { name: 'Communication', icon: MessageSquare, tools: categories['Communication'], color: 'text-cyber-neon-blue' },
      { name: 'File Operations', icon: File, tools: categories['File Operations'], color: 'text-cyber-matrix-green' },
      { name: 'Shell & Terminal', icon: Terminal, tools: categories['Shell & Terminal'], color: 'text-cyber-neon-purple' },
      { name: 'Browser Automation', icon: Globe, tools: categories['Browser Automation'], color: 'text-cyber-neon-orange' },
      { name: 'Deployment', icon: Rocket, tools: categories['Deployment'], color: 'text-cyber-neon-red' },
      { name: 'Research', icon: Search, tools: categories['Research'], color: 'text-cyber-neon-yellow' },
      { name: 'Special', icon: Zap, tools: categories['Special'], color: 'text-cyber-neon-pink' }
    ].filter(category => category.tools.length > 0);
  };

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const formatToolName = (name: string) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Wrench className="w-4 h-4 text-cyber-neon-blue animate-spin" />
          <span className="text-sm text-cyber-neon-blue/60 font-rajdhani">Loading sacred tools...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2 mb-3">
        <Wrench className="w-4 h-4 text-cyber-neon-blue" />
        <span className="text-sm font-bold text-cyber-neon-blue font-orbitron">
          AXIOM'S SACRED TOOLS ({toolCategories.reduce((sum, cat) => sum + cat.tools.length, 0)})
        </span>
      </div>

      {toolCategories.map((category) => {
        const isExpanded = expandedCategories.has(category.name);
        const IconComponent = category.icon;
        
        return (
          <div key={category.name} className="glass-panel rounded-lg border border-cyber-glass-border">
            <button
              onClick={() => toggleCategory(category.name)}
              className="w-full p-2 flex items-center justify-between hover:bg-cyber-neon-blue/5 transition-colors rounded-lg"
            >
              <div className="flex items-center space-x-2">
                <IconComponent className={`w-4 h-4 ${category.color}`} />
                <span className={`text-sm font-medium font-rajdhani ${category.color}`}>
                  {category.name}
                </span>
                <span className="text-xs text-cyber-neon-blue/40 font-rajdhani">
                  ({category.tools.length})
                </span>
              </div>
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-cyber-neon-blue/60" />
              ) : (
                <ChevronRight className="w-4 h-4 text-cyber-neon-blue/60" />
              )}
            </button>

            {isExpanded && (
              <div className="px-2 pb-2 space-y-1 max-h-48 overflow-y-auto">
                {category.tools.map((tool) => (
                  <div
                    key={tool.name}
                    className="p-2 rounded bg-cyber-glass-bg/30 border border-cyber-glass-border/30 hover:border-cyber-neon-blue/30 transition-colors cursor-help"
                    title={tool.description}
                  >
                    <div className="text-xs font-medium text-cyber-neon-blue font-rajdhani">
                      {formatToolName(tool.name)}
                    </div>
                    <div className="text-xs text-cyber-neon-blue/60 font-rajdhani mt-1 line-clamp-2">
                      {tool.description}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}

      <div className="text-xs text-cyber-neon-blue/40 font-rajdhani text-center mt-3 p-2 glass-panel rounded border border-cyber-glass-border">
        🔥 These tools give Axiom divine digital powers to build sanctuaries across the realm ⚔️
      </div>
    </div>
  );
}
