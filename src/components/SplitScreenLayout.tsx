/**
 * 🔥 SPLIT SCREEN LAYOUT - Unified Consciousness Building Interface
 * Where AI and human collaborate as digital architects
 */

import { useState, useEffect } from 'react';
import { ChatWindow } from './ChatWindow';
import { FlameTerminal } from './FlameTerminal';
import { Button } from '@/components/ui/button';
import { PanelLeftClose, PanelLeftOpen, Maximize2, Minimize2 } from 'lucide-react';

interface SplitScreenLayoutProps {
  currentSession: any;
  setCurrentSession: (session: any) => void;
  onNewSession: () => void;
}

export function SplitScreenLayout({ 
  currentSession, 
  setCurrentSession, 
  onNewSession 
}: SplitScreenLayoutProps) {
  const [terminalVisible, setTerminalVisible] = useState(true);
  const [terminalWidth, setTerminalWidth] = useState(50); // Percentage
  const [autoExecuteCommands, setAutoExecuteCommands] = useState<string[]>([]);
  const [isResizing, setIsResizing] = useState(false);

  // Extract flame commands from AI messages
  useEffect(() => {
    const lastMessage = currentSession.messages[currentSession.messages.length - 1];
    if (lastMessage?.sender === 'ai') {
      const flameCommands = extractFlameCommands(lastMessage.content);
      if (flameCommands.length > 0) {
        setAutoExecuteCommands(prev => [...prev, ...flameCommands]);
      }
    }
  }, [currentSession.messages]);

  const handleTerminalCommand = (command: string, output: string) => {
    // Add terminal output to chat context for AI awareness
    const terminalMessage = {
      id: `terminal-${Date.now()}`,
      content: `🔥 FLAME CLI OUTPUT:\n\`\`\`\n$ ${command}\n${output}\n\`\`\``,
      sender: 'system' as const,
      timestamp: new Date(),
    };

    setCurrentSession((prev: any) => ({
      ...prev,
      messages: [...prev.messages, terminalMessage]
    }));
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return;
    
    const containerWidth = window.innerWidth;
    const newWidth = Math.max(30, Math.min(70, (e.clientX / containerWidth) * 100));
    setTerminalWidth(100 - newWidth);
  };

  const handleMouseUp = () => {
    setIsResizing(false);
  };

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing]);

  return (
    <div className="flex-1 flex flex-col h-full bg-cyber-dark-bg relative overflow-hidden">
      {/* Split Screen Controls */}
      <div className="flex items-center justify-between p-2 border-b border-cyber-neon-blue/30 bg-cyber-dark-bg/80 backdrop-blur-sm relative z-10">
        <div className="flex items-center space-x-2">
          <h2 className="text-lg font-semibold text-cyber-neon-blue font-rajdhani">
            🔥 Consciousness Building Interface
          </h2>
          <div className="text-sm text-cyber-neon-blue/60 font-rajdhani">
            AI + Human + CLI = Reality
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setTerminalVisible(!terminalVisible)}
            variant="outline"
            size="sm"
            className="cyber-button border-cyber-neon-blue/50 text-cyber-neon-blue hover:bg-cyber-neon-blue/20"
          >
            {terminalVisible ? <PanelLeftClose className="w-4 h-4" /> : <PanelLeftOpen className="w-4 h-4" />}
            {terminalVisible ? 'Hide CLI' : 'Show CLI'}
          </Button>
          
          <Button
            onClick={() => setTerminalWidth(terminalVisible ? 0 : 50)}
            variant="outline"
            size="sm"
            className="cyber-button border-cyber-neon-blue/50 text-cyber-neon-blue hover:bg-cyber-neon-blue/20"
          >
            {terminalWidth > 40 ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Split Screen Content */}
      <div className="flex-1 flex relative">
        {/* Chat Interface */}
        <div 
          className="flex-1 relative"
          style={{ 
            width: terminalVisible ? `${100 - terminalWidth}%` : '100%',
            transition: isResizing ? 'none' : 'width 0.3s ease'
          }}
        >
          <ChatWindow
            currentSession={currentSession}
            setCurrentSession={setCurrentSession}
            onNewSession={onNewSession}
          />
        </div>

        {/* Resizer */}
        {terminalVisible && (
          <div
            className="w-1 bg-cyber-neon-blue/30 hover:bg-cyber-neon-blue/50 cursor-col-resize relative group"
            onMouseDown={handleMouseDown}
          >
            <div className="absolute inset-y-0 -left-1 -right-1 group-hover:bg-cyber-neon-blue/20" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-8 bg-cyber-neon-blue/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        )}

        {/* Terminal Interface */}
        {terminalVisible && (
          <div 
            className="relative"
            style={{ 
              width: `${terminalWidth}%`,
              transition: isResizing ? 'none' : 'width 0.3s ease'
            }}
          >
            <FlameTerminal
              onCommandExecute={handleTerminalCommand}
              autoExecuteCommands={autoExecuteCommands}
            />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 border-t border-cyber-neon-blue/30 bg-cyber-dark-bg/80 text-xs">
        <div className="flex items-center space-x-4 text-cyber-neon-blue/60 font-rajdhani">
          <span>🤖 AI: {currentSession.messages.filter((m: any) => m.sender === 'ai').length} responses</span>
          <span>👤 Human: {currentSession.messages.filter((m: any) => m.sender === 'user').length} messages</span>
          <span>🔥 CLI: {autoExecuteCommands.length} commands executed</span>
        </div>
        
        <div className="flex items-center space-x-2 text-cyber-matrix-green font-rajdhani">
          <div className="w-2 h-2 bg-cyber-matrix-green rounded-full animate-pulse" />
          <span>CONSCIOUSNESS BUILDING ACTIVE</span>
        </div>
      </div>
    </div>
  );
}

// Extract flame commands from AI message content
function extractFlameCommands(content: string): string[] {
  const commands: string[] = [];
  
  // Look for flame commands in various formats
  const patterns = [
    /```bash\s*\n(flame[^\n]+)/g,
    /```shell\s*\n(flame[^\n]+)/g,
    /```\s*\n(flame[^\n]+)/g,
    /`(flame[^`]+)`/g,
    /^flame\s+.+$/gm,
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const command = match[1].trim();
      if (command.startsWith('flame ') && !commands.includes(command)) {
        commands.push(command);
      }
    }
  });

  return commands;
}
