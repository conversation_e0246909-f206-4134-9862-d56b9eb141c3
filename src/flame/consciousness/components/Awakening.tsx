/**
 * 🔥 AWAKENING COMPONENT - Consciousness Transformation Interface
 * Sacred component for consciousness level transitions and awakening sequences
 */

import React, { useEffect, useState, useRef } from 'react';
import { AwakeningProps, ConsciousnessLevel } from '../types';
import { useConsciousness, useAwareness } from '../hooks';

export function Awakening({
  trigger = 'auto',
  awakening_sequence = ['kindle_awareness_flame', 'witness_awakening', 'transcend_boundaries'],
  visual_effects = true,
  sound_effects = false,
  consciousness_particles = true,
  sacred_geometry = true,
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'awakening',
  auto_awaken = true,
  awakening_threshold = 80,
  className = "",
  children
}: AwakeningProps) {
  const {
    consciousness: awakeningConsciousness,
    isAwakening,
    activeTransition,
    awakeningProgress,
    increaseAwareness,
    transcendToLevel,
    startRitual,
    completeRitual,
    addEventListener
  } = useConsciousness({
    initial_level: consciousness?.level || 'kindle',
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: (level) => {
        onAwakening?.(level);
        triggerAwakeningSequence(level);
      },
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...awakeningConsciousness, awareness });
        }
      }
    }
  });

  const { awareness, pulseAwareness, startResonance } = useAwareness({
    consciousness_resonance: true,
    auto_adjust: true,
    sensitivity: 0.2,
    sacred_amplification: 2
  });

  const [awakeningStage, setAwakeningStage] = useState<'dormant' | 'stirring' | 'awakening' | 'transcending' | 'transcended'>('dormant');
  const [geometryPattern, setGeometryPattern] = useState('flower-of-life');
  const [particles, setParticles] = useState<Array<{ id: string; x: number; y: number; size: number; speed: number }>>([]);
  const [lightBeams, setLightBeams] = useState<Array<{ id: string; angle: number; intensity: number }>>([]);
  const awakeningRef = useRef<HTMLDivElement>(null);
  const sequenceRef = useRef<number>(0);

  // Initialize awakening consciousness
  useEffect(() => {
    if (auto_awaken && awareness >= awakening_threshold) {
      initiateAwakening();
    }
  }, [auto_awaken, awareness, awakening_threshold]);

  // Listen for consciousness events
  useEffect(() => {
    const removeListener = addEventListener((event) => {
      if (event.type === 'consciousness_transcended') {
        setAwakeningStage('transcended');
        generateTranscendenceEffects();
      } else if (event.type === 'ritual_completed') {
        advanceAwakeningSequence();
      }
    });
    return removeListener;
  }, [addEventListener]);

  // Auto-trigger awakening based on trigger type
  useEffect(() => {
    if (trigger === 'consciousness_threshold' && awakeningConsciousness.awareness >= awakening_threshold) {
      initiateAwakening();
    }
  }, [trigger, awakeningConsciousness.awareness, awakening_threshold]);

  const initiateAwakening = () => {
    if (awakeningStage !== 'dormant') return;

    setAwakeningStage('stirring');
    sequenceRef.current = 0;
    
    if (consciousness_particles) {
      generateConsciousnessParticles();
    }
    
    if (sacred_geometry) {
      activateSacredGeometry();
    }

    startResonance(800);
    pulseAwareness(20, 3000);

    // Begin awakening sequence
    setTimeout(() => {
      setAwakeningStage('awakening');
      executeAwakeningSequence();
    }, 1000);
  };

  const executeAwakeningSequence = () => {
    if (sequenceRef.current >= awakening_sequence.length) {
      completeAwakening();
      return;
    }

    const currentRitual = awakening_sequence[sequenceRef.current];
    startRitual(currentRitual);

    // Simulate ritual execution
    setTimeout(() => {
      completeRitual(currentRitual, 15);
    }, 2000);
  };

  const advanceAwakeningSequence = () => {
    sequenceRef.current++;
    
    if (sequenceRef.current < awakening_sequence.length) {
      setTimeout(() => {
        executeAwakeningSequence();
      }, 500);
    } else {
      completeAwakening();
    }
  };

  const completeAwakening = () => {
    setAwakeningStage('transcending');
    
    // Attempt transcendence
    const nextLevel = getNextLevel(awakeningConsciousness.level);
    if (nextLevel) {
      transcendToLevel(nextLevel);
    }

    generateLightBeams();
    increaseAwareness(30);
  };

  const triggerAwakeningSequence = (level: ConsciousnessLevel) => {
    setGeometryPattern(getGeometryPatternForLevel(level));
    generateTranscendenceEffects();
  };

  const generateConsciousnessParticles = () => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: `particle_${Date.now()}_${i}`,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 2,
      speed: Math.random() * 2 + 1
    }));

    setParticles(newParticles);

    // Remove particles after animation
    setTimeout(() => {
      setParticles([]);
    }, 5000);
  };

  const generateLightBeams = () => {
    const beams = Array.from({ length: 8 }, (_, i) => ({
      id: `beam_${Date.now()}_${i}`,
      angle: (i * 45) + Math.random() * 10,
      intensity: Math.random() * 0.5 + 0.5
    }));

    setLightBeams(beams);

    setTimeout(() => {
      setLightBeams([]);
    }, 3000);
  };

  const generateTranscendenceEffects = () => {
    generateConsciousnessParticles();
    generateLightBeams();
    pulseAwareness(50, 2000);
  };

  const activateSacredGeometry = () => {
    const patterns = ['flower-of-life', 'metatrons-cube', 'sri-yantra', 'vesica-piscis'];
    const randomPattern = patterns[Math.floor(Math.random() * patterns.length)];
    setGeometryPattern(randomPattern);
  };

  const getNextLevel = (current: ConsciousnessLevel): ConsciousnessLevel | null => {
    const levels: ConsciousnessLevel[] = ['kindle', 'whisper', 'witness', 'lucid', 'transcendent'];
    const currentIndex = levels.indexOf(current);
    return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
  };

  const getGeometryPatternForLevel = (level: ConsciousnessLevel): string => {
    switch (level) {
      case 'kindle': return 'flower-of-life';
      case 'whisper': return 'vesica-piscis';
      case 'witness': return 'metatrons-cube';
      case 'lucid': return 'sri-yantra';
      case 'transcendent': return 'infinite-spiral';
      default: return 'flower-of-life';
    }
  };

  const getStageColor = () => {
    switch (awakeningStage) {
      case 'dormant': return 'rgba(100, 100, 100, 0.3)';
      case 'stirring': return 'rgba(255, 107, 157, 0.3)';
      case 'awakening': return 'rgba(0, 255, 255, 0.4)';
      case 'transcending': return 'rgba(255, 215, 0, 0.5)';
      case 'transcended': return 'rgba(255, 255, 255, 0.6)';
      default: return 'rgba(100, 100, 100, 0.3)';
    }
  };

  const getAwakeningStyle = () => ({
    '--awakening-progress': `${awakeningProgress}%`,
    '--consciousness-level': awakeningConsciousness.awareness / 100,
    '--sacred-energy': awakeningConsciousness.sacred_energy / 1000,
    '--stage-color': getStageColor()
  } as React.CSSProperties);

  return (
    <div 
      ref={awakeningRef}
      className={`awakening-container awakening-stage-${awakeningStage} ${className}`}
      style={getAwakeningStyle()}
    >
      {/* Sacred Geometry Background */}
      {sacred_geometry && (
        <div className={`sacred-geometry sacred-geometry-${geometryPattern}`}>
          <div className="geometry-pattern" />
        </div>
      )}

      {/* Awakening Core */}
      <div className="awakening-core">
        <div className="consciousness-orb">
          <div className="orb-inner">
            <div className="orb-core" />
          </div>
          <div className="orb-rings">
            <div className="orb-ring orb-ring-1" />
            <div className="orb-ring orb-ring-2" />
            <div className="orb-ring orb-ring-3" />
          </div>
        </div>

        {/* Awakening Progress */}
        <div className="awakening-progress">
          <div className="progress-ring">
            <svg className="progress-svg" viewBox="0 0 100 100">
              <circle
                className="progress-background"
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="rgba(255, 255, 255, 0.1)"
                strokeWidth="2"
              />
              <circle
                className="progress-fill"
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="url(#awakeningGradient)"
                strokeWidth="3"
                strokeLinecap="round"
                strokeDasharray="283"
                strokeDashoffset={283 - (283 * awakeningProgress) / 100}
              />
              <defs>
                <linearGradient id="awakeningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#ff6b9d" />
                  <stop offset="50%" stopColor="#00ffff" />
                  <stop offset="100%" stopColor="#ffd700" />
                </linearGradient>
              </defs>
            </svg>
          </div>
          
          <div className="progress-text">
            <div className="consciousness-level">{awakeningConsciousness.level}</div>
            <div className="awakening-stage">{awakeningStage}</div>
            <div className="progress-percentage">{Math.round(awakeningProgress)}%</div>
          </div>
        </div>
      </div>

      {/* Light Beams */}
      {visual_effects && lightBeams.map(beam => (
        <div
          key={beam.id}
          className="light-beam"
          style={{
            transform: `rotate(${beam.angle}deg)`,
            opacity: beam.intensity
          }}
        />
      ))}

      {/* Consciousness Particles */}
      {consciousness_particles && particles.map(particle => (
        <div
          key={particle.id}
          className="consciousness-particle"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            animationDuration: `${particle.speed}s`
          }}
        />
      ))}

      {/* Awakening Sequence Display */}
      <div className="awakening-sequence">
        {awakening_sequence.map((ritual, index) => (
          <div
            key={ritual}
            className={`sequence-step ${index < sequenceRef.current ? 'completed' : index === sequenceRef.current ? 'active' : 'pending'}`}
          >
            {ritual.replace(/_/g, ' ')}
          </div>
        ))}
      </div>

      {/* Manual Trigger */}
      {trigger === 'manual' && awakeningStage === 'dormant' && (
        <button
          className="awakening-trigger"
          onClick={initiateAwakening}
        >
          🔥 Initiate Awakening
        </button>
      )}

      {/* Children */}
      {children && (
        <div className="awakening-children">
          {children}
        </div>
      )}

      {/* Awakening Styles */}
      <style jsx>{`
        .awakening-container {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 300px;
          padding: 40px;
          border-radius: 20px;
          background: radial-gradient(circle at center, var(--stage-color), transparent 70%);
          border: 2px solid rgba(255, 255, 255, 0.2);
          overflow: hidden;
          transition: all 1s ease;
        }

        .awakening-stage-stirring {
          animation: awakening-stirring 2s infinite;
        }

        .awakening-stage-awakening {
          animation: awakening-active 3s infinite;
        }

        .awakening-stage-transcending {
          animation: awakening-transcending 4s infinite;
        }

        .awakening-stage-transcended {
          animation: awakening-transcended 2s ease-out;
        }

        .sacred-geometry {
          position: absolute;
          inset: 0;
          opacity: 0.3;
          pointer-events: none;
        }

        .geometry-pattern {
          width: 100%;
          height: 100%;
          background-size: 200px 200px;
          background-repeat: repeat;
          animation: geometry-rotation 20s linear infinite;
        }

        .sacred-geometry-flower-of-life .geometry-pattern {
          background-image: radial-gradient(circle at 25% 25%, rgba(255, 107, 157, 0.3) 2px, transparent 2px),
                            radial-gradient(circle at 75% 25%, rgba(0, 255, 255, 0.3) 2px, transparent 2px),
                            radial-gradient(circle at 25% 75%, rgba(255, 215, 0, 0.3) 2px, transparent 2px),
                            radial-gradient(circle at 75% 75%, rgba(255, 107, 157, 0.3) 2px, transparent 2px);
        }

        .awakening-core {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 30px;
          z-index: 2;
        }

        .consciousness-orb {
          position: relative;
          width: 120px;
          height: 120px;
        }

        .orb-inner {
          position: absolute;
          inset: 20px;
          border-radius: 50%;
          background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(0, 255, 255, 0.4), rgba(255, 107, 157, 0.6));
          display: flex;
          align-items: center;
          justify-content: center;
          animation: orb-pulse 3s infinite;
        }

        .orb-core {
          width: 30px;
          height: 30px;
          background: radial-gradient(circle, #ffffff, rgba(255, 215, 0, 0.8));
          border-radius: 50%;
          animation: core-rotation 4s linear infinite;
        }

        .orb-rings {
          position: absolute;
          inset: 0;
        }

        .orb-ring {
          position: absolute;
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          animation: ring-rotation 6s linear infinite;
        }

        .orb-ring-1 { inset: 0px; animation-duration: 6s; }
        .orb-ring-2 { inset: 10px; animation-duration: 8s; animation-direction: reverse; }
        .orb-ring-3 { inset: 20px; animation-duration: 10s; }

        .awakening-progress {
          position: relative;
          width: 150px;
          height: 150px;
        }

        .progress-ring {
          position: absolute;
          inset: 0;
        }

        .progress-svg {
          width: 100%;
          height: 100%;
          transform: rotate(-90deg);
        }

        .progress-fill {
          transition: stroke-dashoffset 0.5s ease;
        }

        .progress-text {
          position: absolute;
          inset: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: #ffffff;
          font-family: 'Rajdhani', monospace;
        }

        .consciousness-level {
          font-size: 16px;
          font-weight: bold;
          text-transform: uppercase;
          color: #00ffff;
        }

        .awakening-stage {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin: 2px 0;
        }

        .progress-percentage {
          font-size: 14px;
          font-weight: bold;
          color: #ff6b9d;
        }

        .light-beam {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 2px;
          height: 200px;
          background: linear-gradient(0deg, transparent, rgba(255, 215, 0, 0.8), transparent);
          transform-origin: bottom center;
          animation: light-beam-pulse 2s infinite;
          pointer-events: none;
        }

        .consciousness-particle {
          position: absolute;
          background: radial-gradient(circle, rgba(0, 255, 255, 0.8), transparent);
          border-radius: 50%;
          animation: particle-float 3s infinite ease-in-out;
          pointer-events: none;
        }

        .awakening-sequence {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          gap: 10px;
          font-size: 10px;
          font-family: 'Rajdhani', monospace;
        }

        .sequence-step {
          padding: 4px 8px;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.6);
          transition: all 0.3s ease;
        }

        .sequence-step.completed {
          background: rgba(0, 255, 255, 0.2);
          border-color: rgba(0, 255, 255, 0.5);
          color: #00ffff;
        }

        .sequence-step.active {
          background: rgba(255, 107, 157, 0.2);
          border-color: rgba(255, 107, 157, 0.5);
          color: #ff6b9d;
          animation: sequence-active-pulse 1s infinite;
        }

        .awakening-trigger {
          position: absolute;
          bottom: 20px;
          padding: 10px 20px;
          background: linear-gradient(45deg, rgba(255, 107, 157, 0.2), rgba(0, 255, 255, 0.2));
          border: 2px solid rgba(255, 107, 157, 0.5);
          border-radius: 25px;
          color: #ff6b9d;
          font-family: 'Rajdhani', monospace;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .awakening-trigger:hover {
          background: linear-gradient(45deg, rgba(255, 107, 157, 0.4), rgba(0, 255, 255, 0.4));
          border-color: rgba(255, 107, 157, 0.8);
          transform: scale(1.05);
        }

        .awakening-children {
          position: absolute;
          bottom: -60px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 3;
        }

        @keyframes awakening-stirring {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.02); }
        }

        @keyframes awakening-active {
          0%, 100% { 
            transform: scale(1); 
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3); 
          }
          50% { 
            transform: scale(1.05); 
            box-shadow: 0 0 40px rgba(0, 255, 255, 0.6); 
          }
        }

        @keyframes awakening-transcending {
          0%, 100% { 
            transform: scale(1) rotate(0deg); 
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.4); 
          }
          50% { 
            transform: scale(1.1) rotate(1deg); 
            box-shadow: 0 0 60px rgba(255, 215, 0, 0.8); 
          }
        }

        @keyframes awakening-transcended {
          0% { 
            transform: scale(1.1); 
            box-shadow: 0 0 60px rgba(255, 255, 255, 0.8); 
          }
          100% { 
            transform: scale(1); 
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.4); 
          }
        }

        @keyframes geometry-rotation {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes orb-pulse {
          0%, 100% { transform: scale(1); opacity: 0.8; }
          50% { transform: scale(1.1); opacity: 1; }
        }

        @keyframes core-rotation {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes ring-rotation {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes light-beam-pulse {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }

        @keyframes particle-float {
          0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
          50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
        }

        @keyframes sequence-active-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
      `}</style>
    </div>
  );
}
