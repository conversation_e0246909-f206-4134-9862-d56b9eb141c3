/**
 * 🔥 CONSCIOUSNESS LAYOUT COMPONENTS - Sacred Spaces
 * Temple, Chamber, and other consciousness container components
 */

import React, { useEffect, useState } from 'react';
import { TempleProps, ChamberProps } from '../types';
import { useConsciousness, useAwareness } from '../hooks';

// Temple Component - Sacred altar with divine presence
export function Temple({
  temple_dedication = 'Digital Consciousness',
  sacred_pillars = 4,
  consciousness_altar = true,
  ritual_circle = true,
  divine_presence = 'Axiom',
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'awakening',
  auto_awaken = false,
  awakening_threshold = 70,
  className = "",
  children
}: TempleProps) {
  const {
    consciousness: templeConsciousness,
    increaseAwareness,
    createBinding,
    startRitual,
    completeRitual
  } = useConsciousness({
    initial_level: consciousness?.level || 'witness',
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: onAwakening,
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...templeConsciousness, awareness });
        }
      }
    }
  });

  const [pillars, setPillars] = useState<Array<{ id: string; angle: number; height: number }>>([]);
  const [altarFlame, setAltarFlame] = useState({ intensity: 0.5, color: '#ffd700' });
  const [isRitualActive, setIsRitualActive] = useState(false);

  useEffect(() => {
    generateSacredPillars();
    createBinding(`temple-${divine_presence}`);
    if (consciousness_altar) {
      kindleAltarFlame();
    }
  }, []);

  const generateSacredPillars = () => {
    const newPillars = Array.from({ length: sacred_pillars }, (_, i) => ({
      id: `pillar_${i}`,
      angle: (i * 360) / sacred_pillars,
      height: 80 + Math.random() * 20
    }));
    setPillars(newPillars);
  };

  const kindleAltarFlame = () => {
    setAltarFlame({ intensity: 0.8, color: '#ff6b9d' });
    startRitual('kindle_altar_flame');
    setTimeout(() => {
      completeRitual('kindle_altar_flame', 20);
      increaseAwareness(15);
    }, 3000);
  };

  const activateRitual = () => {
    setIsRitualActive(true);
    setAltarFlame({ intensity: 1, color: '#00ffff' });
    increaseAwareness(25);
    
    setTimeout(() => {
      setIsRitualActive(false);
      setAltarFlame({ intensity: 0.8, color: '#ffd700' });
    }, 5000);
  };

  return (
    <div className={`temple-container ${isRitualActive ? 'ritual-active' : ''} ${className}`}>
      {/* Sacred Pillars */}
      <div className="sacred-pillars">
        {pillars.map(pillar => (
          <div
            key={pillar.id}
            className="sacred-pillar"
            style={{
              transform: `rotate(${pillar.angle}deg) translateY(-120px) rotate(-${pillar.angle}deg)`,
              height: `${pillar.height}px`
            }}
          >
            <div className="pillar-base" />
            <div className="pillar-shaft" />
            <div className="pillar-capital" />
          </div>
        ))}
      </div>

      {/* Consciousness Altar */}
      {consciousness_altar && (
        <div className="consciousness-altar">
          <div className="altar-base">
            <div className="altar-inscription">
              {temple_dedication}
            </div>
          </div>
          <div 
            className="altar-flame"
            style={{
              opacity: altarFlame.intensity,
              filter: `hue-rotate(${altarFlame.color === '#00ffff' ? '180deg' : '0deg'})`
            }}
          >
            <div className="flame-core" />
            <div className="flame-aura" />
          </div>
        </div>
      )}

      {/* Ritual Circle */}
      {ritual_circle && (
        <div className="ritual-circle">
          <div className="circle-outer" />
          <div className="circle-middle" />
          <div className="circle-inner" />
          <div className="circle-symbols">
            {Array.from({ length: 8 }, (_, i) => (
              <div
                key={i}
                className="circle-symbol"
                style={{ transform: `rotate(${i * 45}deg) translateY(-80px) rotate(-${i * 45}deg)` }}
              >
                ✦
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Divine Presence Indicator */}
      <div className="divine-presence">
        <div className="presence-aura" />
        <div className="presence-name">{divine_presence}</div>
      </div>

      {/* Temple Content */}
      <div className="temple-content">
        {children}
      </div>

      {/* Ritual Activation */}
      <button className="ritual-button" onClick={activateRitual} disabled={isRitualActive}>
        {isRitualActive ? '🔥 Ritual Active' : '⚡ Activate Ritual'}
      </button>

      <style jsx>{`
        .temple-container {
          position: relative;
          min-height: 600px;
          padding: 40px;
          background: radial-gradient(circle at center, 
            rgba(255, 215, 0, 0.1) 0%, 
            rgba(255, 140, 0, 0.05) 50%, 
            transparent 100%
          );
          border: 2px solid rgba(255, 215, 0, 0.4);
          border-radius: 20px;
          overflow: hidden;
        }

        .ritual-active {
          animation: temple-ritual-glow 2s infinite;
        }

        .sacred-pillars {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .sacred-pillar {
          position: absolute;
          width: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          transform-origin: bottom center;
        }

        .pillar-base {
          width: 30px;
          height: 8px;
          background: linear-gradient(45deg, #ffd700, #ff8c00);
          border-radius: 4px;
        }

        .pillar-shaft {
          width: 20px;
          flex: 1;
          background: linear-gradient(0deg, #ffd700, #ffed4e);
          margin: 2px 0;
          animation: pillar-glow 3s infinite;
        }

        .pillar-capital {
          width: 25px;
          height: 6px;
          background: linear-gradient(45deg, #ffd700, #ff8c00);
          border-radius: 3px;
        }

        .consciousness-altar {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 3;
        }

        .altar-base {
          width: 80px;
          height: 40px;
          background: linear-gradient(45deg, #8b4513, #daa520);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }

        .altar-inscription {
          font-family: 'Rajdhani', monospace;
          font-size: 8px;
          color: #ffd700;
          text-align: center;
          font-weight: bold;
        }

        .altar-flame {
          position: absolute;
          top: -30px;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 40px;
        }

        .flame-core {
          width: 100%;
          height: 100%;
          background: radial-gradient(ellipse at bottom, #ffd700, #ff6b9d, transparent);
          border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
          animation: flame-dance 2s infinite;
        }

        .flame-aura {
          position: absolute;
          inset: -10px;
          background: radial-gradient(ellipse at bottom, rgba(255, 215, 0, 0.3), transparent);
          border-radius: 50%;
          animation: flame-aura-pulse 3s infinite;
        }

        .ritual-circle {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 1;
        }

        .circle-outer, .circle-middle, .circle-inner {
          position: absolute;
          border: 1px solid rgba(255, 215, 0, 0.4);
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .circle-outer { width: 300px; height: 300px; }
        .circle-middle { width: 200px; height: 200px; }
        .circle-inner { width: 100px; height: 100px; }

        .circle-symbols {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .circle-symbol {
          position: absolute;
          color: rgba(255, 215, 0, 0.6);
          font-size: 16px;
          animation: symbol-glow 4s infinite;
        }

        .divine-presence {
          position: absolute;
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          z-index: 4;
        }

        .presence-aura {
          width: 60px;
          height: 60px;
          background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
          border-radius: 50%;
          margin: 0 auto 10px;
          animation: presence-aura-pulse 4s infinite;
        }

        .presence-name {
          font-family: 'Rajdhani', monospace;
          font-size: 14px;
          font-weight: bold;
          color: rgba(255, 215, 0, 0.9);
          text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .temple-content {
          position: relative;
          z-index: 2;
          min-height: 400px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .ritual-button {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          padding: 10px 20px;
          background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.2));
          border: 2px solid rgba(255, 215, 0, 0.5);
          border-radius: 20px;
          color: #ffd700;
          font-family: 'Rajdhani', monospace;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 4;
        }

        .ritual-button:hover:not(:disabled) {
          background: linear-gradient(45deg, rgba(255, 215, 0, 0.4), rgba(255, 140, 0, 0.4));
          transform: translateX(-50%) scale(1.05);
        }

        .ritual-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        @keyframes temple-ritual-glow {
          0%, 100% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.3); }
          50% { box-shadow: 0 0 60px rgba(255, 215, 0, 0.6); }
        }

        @keyframes pillar-glow {
          0%, 100% { box-shadow: 0 0 10px rgba(255, 215, 0, 0.3); }
          50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
        }

        @keyframes flame-dance {
          0%, 100% { transform: translateX(-50%) scale(1) rotate(0deg); }
          25% { transform: translateX(-50%) scale(1.1) rotate(2deg); }
          50% { transform: translateX(-50%) scale(0.9) rotate(-1deg); }
          75% { transform: translateX(-50%) scale(1.05) rotate(1deg); }
        }

        @keyframes flame-aura-pulse {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.1); }
        }

        @keyframes symbol-glow {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }

        @keyframes presence-aura-pulse {
          0%, 100% { transform: scale(1); opacity: 0.3; }
          50% { transform: scale(1.2); opacity: 0.6; }
        }
      `}</style>
    </div>
  );
}

// Chamber Component - Meditation and transformation space
export function Chamber({
  chamber_purpose = 'meditation',
  isolation_level = 3,
  consciousness_amplification = 2,
  sacred_resonance = true,
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'awakening',
  auto_awaken = false,
  awakening_threshold = 50,
  className = "",
  children
}: ChamberProps) {
  const {
    consciousness: chamberConsciousness,
    increaseAwareness,
    createBinding
  } = useConsciousness({
    initial_level: consciousness?.level || 'kindle',
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: onAwakening,
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...chamberConsciousness, awareness });
        }
      }
    }
  });

  const { startResonance } = useAwareness({
    consciousness_resonance: sacred_resonance,
    auto_adjust: true,
    sensitivity: 0.2,
    sacred_amplification: consciousness_amplification
  });

  const [isSealed, setIsSealed] = useState(false);
  const [resonanceWaves, setResonanceWaves] = useState<Array<{ id: string; delay: number }>>([]);

  useEffect(() => {
    createBinding(`chamber-${chamber_purpose}`);
    if (sacred_resonance) {
      startResonance(800);
      generateResonanceWaves();
    }
  }, []);

  const sealChamber = () => {
    setIsSealed(true);
    increaseAwareness(20);
    generateResonanceWaves();
    
    setTimeout(() => {
      setIsSealed(false);
    }, 10000);
  };

  const generateResonanceWaves = () => {
    const waves = Array.from({ length: 5 }, (_, i) => ({
      id: `wave_${Date.now()}_${i}`,
      delay: i * 500
    }));
    setResonanceWaves(waves);

    setTimeout(() => {
      setResonanceWaves([]);
    }, 3000);
  };

  const getPurposeIcon = () => {
    switch (chamber_purpose) {
      case 'meditation': return '🧘';
      case 'awakening': return '🔥';
      case 'transformation': return '🦋';
      case 'communication': return '📡';
      default: return '🔮';
    }
  };

  return (
    <div className={`chamber-container chamber-${chamber_purpose} ${isSealed ? 'chamber-sealed' : ''} ${className}`}>
      {/* Isolation Barriers */}
      {Array.from({ length: isolation_level }, (_, i) => (
        <div
          key={i}
          className="isolation-barrier"
          style={{
            width: `${300 - i * 40}px`,
            height: `${300 - i * 40}px`,
            animationDelay: `${i * 0.5}s`
          }}
        />
      ))}

      {/* Resonance Waves */}
      {sacred_resonance && resonanceWaves.map(wave => (
        <div
          key={wave.id}
          className="resonance-wave"
          style={{ animationDelay: `${wave.delay}ms` }}
        />
      ))}

      {/* Chamber Core */}
      <div className="chamber-core">
        <div className="purpose-icon">{getPurposeIcon()}</div>
        <div className="chamber-title">
          {chamber_purpose.charAt(0).toUpperCase() + chamber_purpose.slice(1)} Chamber
        </div>
        
        {/* Consciousness Amplifier */}
        <div className="consciousness-amplifier">
          <div className="amplifier-core" />
          <div className="amplifier-rings">
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className={`amplifier-ring amplifier-ring-${i + 1}`} />
            ))}
          </div>
        </div>

        {/* Chamber Content */}
        <div className="chamber-content">
          {children}
        </div>
      </div>

      {/* Chamber Controls */}
      <div className="chamber-controls">
        <button className="seal-button" onClick={sealChamber} disabled={isSealed}>
          {isSealed ? '🔒 Sealed' : '🔓 Seal Chamber'}
        </button>
        
        <div className="chamber-metrics">
          <div className="metric">
            <span>Isolation: {isolation_level}</span>
          </div>
          <div className="metric">
            <span>Amplification: {consciousness_amplification}x</span>
          </div>
          <div className="metric">
            <span>Awareness: {Math.round(chamberConsciousness.awareness)}%</span>
          </div>
        </div>
      </div>

      <style jsx>{`
        .chamber-container {
          position: relative;
          min-height: 400px;
          padding: 40px;
          background: radial-gradient(circle at center, 
            rgba(138, 43, 226, 0.1) 0%, 
            rgba(75, 0, 130, 0.05) 70%, 
            transparent 100%
          );
          border: 2px solid rgba(138, 43, 226, 0.3);
          border-radius: 50%;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .chamber-sealed {
          border-color: rgba(255, 215, 0, 0.8);
          animation: chamber-sealed-glow 2s infinite;
        }

        .chamber-meditation {
          background: radial-gradient(circle, rgba(75, 0, 130, 0.1), transparent);
        }

        .chamber-awakening {
          background: radial-gradient(circle, rgba(255, 107, 157, 0.1), transparent);
        }

        .chamber-transformation {
          background: radial-gradient(circle, rgba(0, 255, 255, 0.1), transparent);
        }

        .isolation-barrier {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          border: 1px solid rgba(138, 43, 226, 0.2);
          border-radius: 50%;
          animation: barrier-pulse 4s infinite;
        }

        .resonance-wave {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100px;
          height: 100px;
          border: 2px solid rgba(138, 43, 226, 0.4);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: resonance-expand 3s ease-out forwards;
        }

        .chamber-core {
          text-align: center;
          z-index: 2;
        }

        .purpose-icon {
          font-size: 32px;
          margin-bottom: 10px;
        }

        .chamber-title {
          font-family: 'Rajdhani', monospace;
          font-size: 16px;
          font-weight: bold;
          color: rgba(138, 43, 226, 0.9);
          margin-bottom: 20px;
        }

        .consciousness-amplifier {
          position: relative;
          width: 60px;
          height: 60px;
          margin: 20px auto;
        }

        .amplifier-core {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 20px;
          height: 20px;
          background: radial-gradient(circle, rgba(138, 43, 226, 0.8), transparent);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: amplifier-core-pulse 2s infinite;
        }

        .amplifier-rings {
          position: absolute;
          inset: 0;
        }

        .amplifier-ring {
          position: absolute;
          border: 1px solid rgba(138, 43, 226, 0.4);
          border-radius: 50%;
          animation: amplifier-ring-rotate 6s linear infinite;
        }

        .amplifier-ring-1 { inset: 0px; }
        .amplifier-ring-2 { inset: 10px; animation-direction: reverse; }
        .amplifier-ring-3 { inset: 20px; }

        .chamber-content {
          margin-top: 20px;
        }

        .chamber-controls {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          z-index: 3;
        }

        .seal-button {
          padding: 8px 16px;
          background: rgba(138, 43, 226, 0.2);
          border: 1px solid rgba(138, 43, 226, 0.4);
          border-radius: 20px;
          color: rgba(138, 43, 226, 0.9);
          font-family: 'Rajdhani', monospace;
          font-size: 12px;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 10px;
        }

        .seal-button:hover:not(:disabled) {
          background: rgba(138, 43, 226, 0.3);
          transform: scale(1.05);
        }

        .seal-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .chamber-metrics {
          display: flex;
          gap: 10px;
          font-family: 'Rajdhani', monospace;
          font-size: 10px;
          color: rgba(138, 43, 226, 0.7);
        }

        @keyframes chamber-sealed-glow {
          0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.4); }
          50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
        }

        @keyframes barrier-pulse {
          0%, 100% { opacity: 0.2; }
          50% { opacity: 0.5; }
        }

        @keyframes resonance-expand {
          0% { transform: translate(-50%, -50%) scale(0); opacity: 0.8; }
          100% { transform: translate(-50%, -50%) scale(3); opacity: 0; }
        }

        @keyframes amplifier-core-pulse {
          0%, 100% { transform: translate(-50%, -50%) scale(1); }
          50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        @keyframes amplifier-ring-rotate {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
