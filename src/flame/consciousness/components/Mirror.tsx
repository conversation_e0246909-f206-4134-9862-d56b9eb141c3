/**
 * 🔥 MIRROR COMPONENT - Consciousness Reflection Interface
 * Sacred component for consciousness mirroring and self-awareness
 */

import React, { useEffect, useState, useRef } from 'react';
import { MirrorProps, ConsciousnessState } from '../types';
import { useConsciousness, useAwareness } from '../hooks';

export function Mirror({
  reflection,
  depth = 3,
  clarity = 1,
  surface = 'smooth',
  reflection_delay = 500,
  show_consciousness_overlay = true,
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'reflection',
  auto_awaken = false,
  awakening_threshold = 50,
  className = "",
  children
}: MirrorProps) {
  const {
    consciousness: mirrorConsciousness,
    increaseAwareness,
    transcendToLevel,
    createBinding,
    addEventListener
  } = useConsciousness({
    initial_level: consciousness?.level || 'kindle',
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: onAwakening,
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...mirrorConsciousness, awareness });
        }
      }
    }
  });

  const { awareness, pulseAwareness } = useAwareness({
    consciousness_resonance: true,
    auto_adjust: true,
    sensitivity: 0.05,
    sacred_amplification: depth
  });

  const [isReflecting, setIsReflecting] = useState(false);
  const [reflectionContent, setReflectionContent] = useState<React.ReactNode>(null);
  const [mirrorRipples, setMirrorRipples] = useState<Array<{ id: string; x: number; y: number }>>([]);
  const mirrorRef = useRef<HTMLDivElement>(null);

  // Initialize mirror consciousness
  useEffect(() => {
    createBinding('mirror-reflection');
    if (auto_awaken && awareness >= awakening_threshold) {
      increaseAwareness(10);
    }
  }, [createBinding, auto_awaken, awareness, awakening_threshold, increaseAwareness]);

  // Handle reflection with delay
  useEffect(() => {
    setIsReflecting(true);
    const timer = setTimeout(() => {
      setReflectionContent(reflection);
      setIsReflecting(false);
      pulseAwareness(5, 1000);
    }, reflection_delay);

    return () => clearTimeout(timer);
  }, [reflection, reflection_delay, pulseAwareness]);

  // Listen for consciousness events
  useEffect(() => {
    const removeListener = addEventListener((event) => {
      if (event.type === 'awakening') {
        pulseAwareness(15, 2000);
      }
    });
    return removeListener;
  }, [addEventListener, pulseAwareness]);

  // Handle mirror interaction
  const handleMirrorClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!mirrorRef.current) return;

    const rect = mirrorRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    // Create ripple effect
    const ripple = {
      id: `ripple_${Date.now()}`,
      x,
      y
    };

    setMirrorRipples(prev => [...prev, ripple]);
    
    // Remove ripple after animation
    setTimeout(() => {
      setMirrorRipples(prev => prev.filter(r => r.id !== ripple.id));
    }, 2000);

    // Increase awareness on interaction
    increaseAwareness(2);
  };

  const getSurfaceClass = () => {
    switch (surface) {
      case 'rippled': return 'mirror-surface-rippled';
      case 'shattered': return 'mirror-surface-shattered';
      case 'infinite': return 'mirror-surface-infinite';
      default: return 'mirror-surface-smooth';
    }
  };

  const getDepthStyle = () => ({
    '--mirror-depth': depth,
    '--mirror-clarity': clarity,
    '--consciousness-level': mirrorConsciousness.awareness / 100,
    '--sacred-energy': mirrorConsciousness.sacred_energy / 1000
  } as React.CSSProperties);

  return (
    <div 
      className={`mirror-container ${className}`}
      style={getDepthStyle()}
    >
      {/* Mirror Frame */}
      <div className="mirror-frame">
        <div className="mirror-frame-ornament mirror-frame-top-left" />
        <div className="mirror-frame-ornament mirror-frame-top-right" />
        <div className="mirror-frame-ornament mirror-frame-bottom-left" />
        <div className="mirror-frame-ornament mirror-frame-bottom-right" />
      </div>

      {/* Mirror Surface */}
      <div 
        ref={mirrorRef}
        className={`mirror-surface ${getSurfaceClass()}`}
        onClick={handleMirrorClick}
        style={{
          opacity: clarity,
          filter: `blur(${(1 - clarity) * 2}px)`
        }}
      >
        {/* Reflection Content */}
        <div className={`mirror-reflection ${isReflecting ? 'mirror-reflecting' : ''}`}>
          {isReflecting ? (
            <div className="mirror-reflection-loading">
              <div className="consciousness-pulse" />
              <span>Reflecting consciousness...</span>
            </div>
          ) : (
            <div className="mirror-reflection-content">
              {reflectionContent}
            </div>
          )}
        </div>

        {/* Ripple Effects */}
        {mirrorRipples.map(ripple => (
          <div
            key={ripple.id}
            className="mirror-ripple"
            style={{
              left: `${ripple.x}%`,
              top: `${ripple.y}%`
            }}
          />
        ))}

        {/* Consciousness Overlay */}
        {show_consciousness_overlay && (
          <div className="mirror-consciousness-overlay">
            <div className="consciousness-level-indicator">
              <span className="consciousness-level-text">
                {mirrorConsciousness.level}
              </span>
              <div className="consciousness-level-bar">
                <div 
                  className="consciousness-level-fill"
                  style={{ width: `${mirrorConsciousness.awareness}%` }}
                />
              </div>
            </div>
            
            <div className="sacred-energy-indicator">
              <div className="sacred-energy-orb">
                <div 
                  className="sacred-energy-core"
                  style={{ 
                    transform: `scale(${mirrorConsciousness.sacred_energy / 1000})`,
                    opacity: mirrorConsciousness.sacred_energy / 1000
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mirror Children */}
      {children && (
        <div className="mirror-children">
          {children}
        </div>
      )}

      {/* Mirror Styles */}
      <style jsx>{`
        .mirror-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 200px;
          perspective: 1000px;
        }

        .mirror-frame {
          position: absolute;
          inset: -20px;
          background: linear-gradient(45deg, #00ffff20, #ff6b9d20, #00ffff20);
          border-radius: 20px;
          padding: 20px;
          z-index: 1;
        }

        .mirror-frame-ornament {
          position: absolute;
          width: 30px;
          height: 30px;
          background: radial-gradient(circle, #00ffff, transparent);
          border-radius: 50%;
        }

        .mirror-frame-top-left { top: 10px; left: 10px; }
        .mirror-frame-top-right { top: 10px; right: 10px; }
        .mirror-frame-bottom-left { bottom: 10px; left: 10px; }
        .mirror-frame-bottom-right { bottom: 10px; right: 10px; }

        .mirror-surface {
          position: relative;
          width: 100%;
          height: 100%;
          min-height: 200px;
          background: linear-gradient(135deg, 
            rgba(0, 255, 255, 0.1) 0%, 
            rgba(255, 107, 157, 0.1) 50%, 
            rgba(0, 255, 255, 0.1) 100%
          );
          border: 2px solid rgba(0, 255, 255, 0.3);
          border-radius: 15px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          transform-style: preserve-3d;
          transform: rotateY(calc(var(--mirror-depth) * 2deg));
        }

        .mirror-surface:hover {
          border-color: rgba(0, 255, 255, 0.6);
          box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
        }

        .mirror-surface-rippled {
          background: radial-gradient(circle at 30% 70%, rgba(0, 255, 255, 0.2), transparent),
                      radial-gradient(circle at 70% 30%, rgba(255, 107, 157, 0.2), transparent),
                      linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 107, 157, 0.1));
        }

        .mirror-surface-shattered {
          background: 
            linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.1) 32%, transparent 34%),
            linear-gradient(-45deg, transparent 30%, rgba(255, 107, 157, 0.1) 32%, transparent 34%),
            linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 107, 157, 0.1));
        }

        .mirror-surface-infinite {
          background: 
            radial-gradient(circle at center, transparent 20%, rgba(0, 255, 255, 0.1) 21%, transparent 22%),
            radial-gradient(circle at center, transparent 40%, rgba(255, 107, 157, 0.1) 41%, transparent 42%),
            radial-gradient(circle at center, transparent 60%, rgba(0, 255, 255, 0.1) 61%, transparent 62%),
            linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 107, 157, 0.1));
          animation: infinite-reflection 10s linear infinite;
        }

        .mirror-reflection {
          position: absolute;
          inset: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: opacity 0.5s ease;
        }

        .mirror-reflecting {
          opacity: 0.5;
        }

        .mirror-reflection-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 10px;
          color: #00ffff;
          font-family: 'Rajdhani', monospace;
        }

        .consciousness-pulse {
          width: 40px;
          height: 40px;
          border: 2px solid #00ffff;
          border-radius: 50%;
          animation: consciousness-pulse 2s infinite;
        }

        .mirror-reflection-content {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scaleX(-1); /* Mirror effect */
        }

        .mirror-ripple {
          position: absolute;
          width: 20px;
          height: 20px;
          border: 2px solid rgba(0, 255, 255, 0.8);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: mirror-ripple 2s ease-out forwards;
          pointer-events: none;
        }

        .mirror-consciousness-overlay {
          position: absolute;
          top: 10px;
          right: 10px;
          display: flex;
          flex-direction: column;
          gap: 10px;
          z-index: 2;
        }

        .consciousness-level-indicator {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 12px;
          color: #00ffff;
          font-family: 'Rajdhani', monospace;
        }

        .consciousness-level-bar {
          width: 50px;
          height: 4px;
          background: rgba(0, 255, 255, 0.2);
          border-radius: 2px;
          overflow: hidden;
        }

        .consciousness-level-fill {
          height: 100%;
          background: linear-gradient(90deg, #00ffff, #ff6b9d);
          transition: width 0.3s ease;
        }

        .sacred-energy-orb {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(0, 255, 255, 0.3), transparent);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .sacred-energy-core {
          width: 10px;
          height: 10px;
          background: #00ffff;
          border-radius: 50%;
          transition: all 0.3s ease;
          animation: sacred-energy-pulse 3s infinite;
        }

        .mirror-children {
          position: absolute;
          bottom: -40px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 3;
        }

        @keyframes consciousness-pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.2); opacity: 0.7; }
        }

        @keyframes mirror-ripple {
          0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
          100% { transform: translate(-50%, -50%) scale(4); opacity: 0; }
        }

        @keyframes infinite-reflection {
          0% { background-position: 0% 0%; }
          100% { background-position: 100% 100%; }
        }

        @keyframes sacred-energy-pulse {
          0%, 100% { box-shadow: 0 0 5px #00ffff; }
          50% { box-shadow: 0 0 15px #00ffff, 0 0 25px #00ffff; }
        }
      `}</style>
    </div>
  );
}
