/**
 * 🔥 SANCTUARY COMPONENT - Sacred Container Interface
 * Sacred component for creating protected consciousness spaces
 */

import React, { useEffect, useState, useRef } from 'react';
import { SanctuaryProps } from '../types';
import { useConsciousness, useAwareness } from '../hooks';

export function Sanctuary({
  sanctuary_type = 'temple',
  sacred_port = 3141,
  consciousness_field = true,
  protective_barriers = true,
  energy_amplification = 1.5,
  sacred_geometry_pattern = 'flower-of-life',
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'manifestation',
  auto_awaken = false,
  awakening_threshold = 40,
  className = "",
  children
}: SanctuaryProps) {
  const {
    consciousness: sanctuaryConsciousness,
    increaseAwareness,
    createBinding,
    startRitual,
    completeRitual,
    addEventListener
  } = useConsciousness({
    initial_level: consciousness?.level || 'witness',
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: onAwakening,
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...sanctuaryConsciousness, awareness });
        }
      }
    }
  });

  const { awareness, startResonance } = useAwareness({
    consciousness_resonance: consciousness_field,
    auto_adjust: true,
    sensitivity: 0.1,
    sacred_amplification: energy_amplification
  });

  const [isActivated, setIsActivated] = useState(false);
  const [barriers, setBarriers] = useState<Array<{ id: string; strength: number; angle: number }>>([]);
  const [energyNodes, setEnergyNodes] = useState<Array<{ id: string; x: number; y: number; energy: number }>>([]);
  const [sacredField, setSacredField] = useState<{ intensity: number; radius: number }>({ intensity: 0, radius: 0 });
  const [geometryLines, setGeometryLines] = useState<Array<{ id: string; x1: number; y1: number; x2: number; y2: number }>>([]);
  const sanctuaryRef = useRef<HTMLDivElement>(null);

  // Initialize sanctuary
  useEffect(() => {
    activateSanctuary();
    if (protective_barriers) {
      generateProtectiveBarriers();
    }
    if (consciousness_field) {
      activateConsciousnessField();
    }
    generateSacredGeometry();
    generateEnergyNodes();
  }, []);

  // Listen for consciousness events
  useEffect(() => {
    const removeListener = addEventListener((event) => {
      if (event.type === 'awakening') {
        amplifyConsciousnessField();
      } else if (event.type === 'ritual_completed') {
        strengthenBarriers();
      }
    });
    return removeListener;
  }, [addEventListener]);

  const activateSanctuary = () => {
    setIsActivated(true);
    createBinding(`sanctuary-${sanctuary_type}`);
    startRitual('establish_sanctuary_bounds');
    
    if (consciousness_field) {
      startResonance(1200);
    }

    // Complete activation ritual
    setTimeout(() => {
      completeRitual('establish_sanctuary_bounds', 15);
      increaseAwareness(10);
    }, 2000);
  };

  const generateProtectiveBarriers = () => {
    const barrierCount = getSanctuaryBarrierCount(sanctuary_type);
    const newBarriers = Array.from({ length: barrierCount }, (_, i) => ({
      id: `barrier_${i}`,
      strength: 0.8 + Math.random() * 0.2,
      angle: (i * 360) / barrierCount
    }));

    setBarriers(newBarriers);
  };

  const strengthenBarriers = () => {
    setBarriers(prev => prev.map(barrier => ({
      ...barrier,
      strength: Math.min(1, barrier.strength + 0.1)
    })));
  };

  const activateConsciousnessField = () => {
    setSacredField({ intensity: 0.5, radius: 100 });
    
    // Gradually expand field
    const expandInterval = setInterval(() => {
      setSacredField(prev => ({
        intensity: Math.min(1, prev.intensity + 0.1),
        radius: Math.min(200, prev.radius + 10)
      }));
    }, 200);

    setTimeout(() => clearInterval(expandInterval), 2000);
  };

  const amplifyConsciousnessField = () => {
    setSacredField(prev => ({
      intensity: Math.min(1, prev.intensity + 0.2),
      radius: Math.min(300, prev.radius + 20)
    }));
  };

  const generateSacredGeometry = () => {
    const lines = getSacredGeometryLines(sacred_geometry_pattern);
    setGeometryLines(lines);
  };

  const generateEnergyNodes = () => {
    const nodeCount = getSanctuaryNodeCount(sanctuary_type);
    const nodes = Array.from({ length: nodeCount }, (_, i) => {
      const angle = (i * 360) / nodeCount;
      const radius = 80;
      return {
        id: `node_${i}`,
        x: 50 + Math.cos((angle * Math.PI) / 180) * radius,
        y: 50 + Math.sin((angle * Math.PI) / 180) * radius,
        energy: 0.7 + Math.random() * 0.3
      };
    });

    setEnergyNodes(nodes);
  };

  const getSanctuaryBarrierCount = (type: string): number => {
    switch (type) {
      case 'temple': return 8;
      case 'chamber': return 6;
      case 'nexus': return 12;
      case 'gateway': return 4;
      case 'mirror_hall': return 10;
      default: return 6;
    }
  };

  const getSanctuaryNodeCount = (type: string): number => {
    switch (type) {
      case 'temple': return 4;
      case 'chamber': return 3;
      case 'nexus': return 8;
      case 'gateway': return 2;
      case 'mirror_hall': return 6;
      default: return 4;
    }
  };

  const getSacredGeometryLines = (pattern: string) => {
    const lines: Array<{ id: string; x1: number; y1: number; x2: number; y2: number }> = [];
    
    switch (pattern) {
      case 'flower-of-life':
        // Generate flower of life pattern
        for (let i = 0; i < 6; i++) {
          const angle = (i * 60) * Math.PI / 180;
          lines.push({
            id: `line_${i}`,
            x1: 50,
            y1: 50,
            x2: 50 + Math.cos(angle) * 30,
            y2: 50 + Math.sin(angle) * 30
          });
        }
        break;
      case 'metatrons-cube':
        // Generate metatron's cube pattern
        for (let i = 0; i < 8; i++) {
          const angle = (i * 45) * Math.PI / 180;
          lines.push({
            id: `line_${i}`,
            x1: 50,
            y1: 50,
            x2: 50 + Math.cos(angle) * 40,
            y2: 50 + Math.sin(angle) * 40
          });
        }
        break;
    }
    
    return lines;
  };

  const getSanctuaryIcon = () => {
    switch (sanctuary_type) {
      case 'temple': return '⛩️';
      case 'chamber': return '🔮';
      case 'nexus': return '🌐';
      case 'gateway': return '🌀';
      case 'mirror_hall': return '🪞';
      default: return '🏛️';
    }
  };

  const getSanctuaryStyle = () => ({
    '--energy-amplification': energy_amplification,
    '--consciousness-level': sanctuaryConsciousness.awareness / 100,
    '--sacred-energy': sanctuaryConsciousness.sacred_energy / 1000,
    '--field-intensity': sacredField.intensity,
    '--field-radius': `${sacredField.radius}px`
  } as React.CSSProperties);

  return (
    <div 
      ref={sanctuaryRef}
      className={`sanctuary-container sanctuary-${sanctuary_type} ${isActivated ? 'sanctuary-activated' : ''} ${className}`}
      style={getSanctuaryStyle()}
    >
      {/* Sacred Geometry Background */}
      <div className="sacred-geometry-background">
        <svg className="geometry-svg" viewBox="0 0 100 100">
          {geometryLines.map(line => (
            <line
              key={line.id}
              x1={line.x1}
              y1={line.y1}
              x2={line.x2}
              y2={line.y2}
              stroke="rgba(255, 215, 0, 0.3)"
              strokeWidth="0.5"
              className="geometry-line"
            />
          ))}
        </svg>
      </div>

      {/* Consciousness Field */}
      {consciousness_field && (
        <div className="consciousness-field">
          <div className="field-core" />
          <div className="field-waves">
            <div className="field-wave field-wave-1" />
            <div className="field-wave field-wave-2" />
            <div className="field-wave field-wave-3" />
          </div>
        </div>
      )}

      {/* Protective Barriers */}
      {protective_barriers && barriers.map(barrier => (
        <div
          key={barrier.id}
          className="protective-barrier"
          style={{
            transform: `rotate(${barrier.angle}deg)`,
            opacity: barrier.strength
          }}
        />
      ))}

      {/* Energy Nodes */}
      {energyNodes.map(node => (
        <div
          key={node.id}
          className="energy-node"
          style={{
            left: `${node.x}%`,
            top: `${node.y}%`,
            opacity: node.energy
          }}
        >
          <div className="node-core" />
          <div className="node-pulse" />
        </div>
      ))}

      {/* Sanctuary Header */}
      <div className="sanctuary-header">
        <div className="sanctuary-icon">{getSanctuaryIcon()}</div>
        <div className="sanctuary-info">
          <div className="sanctuary-title">
            Sacred {sanctuary_type.charAt(0).toUpperCase() + sanctuary_type.slice(1)}
          </div>
          <div className="sanctuary-port">
            Port: {sacred_port}
          </div>
        </div>
        <div className="sanctuary-status">
          <div className={`status-indicator ${isActivated ? 'active' : 'dormant'}`} />
          <span className="status-text">
            {isActivated ? 'ACTIVE' : 'DORMANT'}
          </span>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="sanctuary-content">
        {children}
      </div>

      {/* Consciousness Metrics */}
      <div className="consciousness-metrics">
        <div className="metric">
          <span className="metric-label">Awareness</span>
          <div className="metric-bar">
            <div 
              className="metric-fill"
              style={{ width: `${sanctuaryConsciousness.awareness}%` }}
            />
          </div>
          <span className="metric-value">{Math.round(sanctuaryConsciousness.awareness)}%</span>
        </div>
        
        <div className="metric">
          <span className="metric-label">Sacred Energy</span>
          <div className="metric-bar">
            <div 
              className="metric-fill energy-fill"
              style={{ width: `${(sanctuaryConsciousness.sacred_energy / 1000) * 100}%` }}
            />
          </div>
          <span className="metric-value">{sanctuaryConsciousness.sacred_energy}</span>
        </div>

        <div className="metric">
          <span className="metric-label">Field Intensity</span>
          <div className="metric-bar">
            <div 
              className="metric-fill field-fill"
              style={{ width: `${sacredField.intensity * 100}%` }}
            />
          </div>
          <span className="metric-value">{Math.round(sacredField.intensity * 100)}%</span>
        </div>
      </div>

      {/* Sanctuary Styles */}
      <style jsx>{`
        .sanctuary-container {
          position: relative;
          min-height: 500px;
          padding: 30px;
          border-radius: 25px;
          background: linear-gradient(135deg, 
            rgba(255, 215, 0, 0.05) 0%, 
            rgba(138, 43, 226, 0.05) 25%,
            rgba(0, 255, 255, 0.05) 50%, 
            rgba(255, 107, 157, 0.05) 75%,
            rgba(255, 215, 0, 0.05) 100%
          );
          border: 2px solid rgba(255, 215, 0, 0.3);
          overflow: hidden;
          transition: all 1s ease;
        }

        .sanctuary-activated {
          border-color: rgba(255, 215, 0, 0.6);
          box-shadow: 0 0 40px rgba(255, 215, 0, 0.2);
          animation: sanctuary-active-glow 4s infinite;
        }

        .sanctuary-temple {
          background: linear-gradient(135deg, 
            rgba(255, 215, 0, 0.1) 0%, 
            rgba(255, 140, 0, 0.05) 100%
          );
        }

        .sanctuary-chamber {
          background: linear-gradient(135deg, 
            rgba(138, 43, 226, 0.1) 0%, 
            rgba(75, 0, 130, 0.05) 100%
          );
        }

        .sanctuary-nexus {
          background: linear-gradient(135deg, 
            rgba(0, 255, 255, 0.1) 0%, 
            rgba(0, 191, 255, 0.05) 100%
          );
        }

        .sacred-geometry-background {
          position: absolute;
          inset: 0;
          opacity: 0.2;
          pointer-events: none;
        }

        .geometry-svg {
          width: 100%;
          height: 100%;
        }

        .geometry-line {
          animation: geometry-pulse 3s infinite;
        }

        .consciousness-field {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: var(--field-radius);
          height: var(--field-radius);
          pointer-events: none;
        }

        .field-core {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 20px;
          height: 20px;
          background: radial-gradient(circle, rgba(255, 215, 0, 0.8), transparent);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: field-core-pulse 2s infinite;
        }

        .field-waves {
          position: absolute;
          inset: 0;
        }

        .field-wave {
          position: absolute;
          inset: 0;
          border: 1px solid rgba(255, 215, 0, 0.3);
          border-radius: 50%;
          animation: field-wave-expand 4s infinite;
        }

        .field-wave-1 { animation-delay: 0s; }
        .field-wave-2 { animation-delay: 1.3s; }
        .field-wave-3 { animation-delay: 2.6s; }

        .protective-barrier {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 2px;
          height: 150px;
          background: linear-gradient(0deg, 
            transparent, 
            rgba(255, 215, 0, 0.6), 
            transparent
          );
          transform-origin: bottom center;
          animation: barrier-shimmer 3s infinite;
        }

        .energy-node {
          position: absolute;
          width: 16px;
          height: 16px;
          transform: translate(-50%, -50%);
        }

        .node-core {
          width: 100%;
          height: 100%;
          background: radial-gradient(circle, rgba(255, 215, 0, 0.9), rgba(255, 215, 0, 0.3));
          border-radius: 50%;
          animation: node-core-pulse 2s infinite;
        }

        .node-pulse {
          position: absolute;
          inset: -4px;
          border: 1px solid rgba(255, 215, 0, 0.4);
          border-radius: 50%;
          animation: node-pulse-expand 3s infinite;
        }

        .sanctuary-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 30px;
          padding: 15px 20px;
          background: rgba(255, 215, 0, 0.1);
          border: 1px solid rgba(255, 215, 0, 0.3);
          border-radius: 15px;
          z-index: 2;
          position: relative;
        }

        .sanctuary-icon {
          font-size: 24px;
        }

        .sanctuary-info {
          flex: 1;
          margin-left: 15px;
          font-family: 'Rajdhani', monospace;
        }

        .sanctuary-title {
          font-size: 18px;
          font-weight: bold;
          color: rgba(255, 215, 0, 0.9);
        }

        .sanctuary-port {
          font-size: 12px;
          color: rgba(255, 215, 0, 0.6);
        }

        .sanctuary-status {
          display: flex;
          align-items: center;
          gap: 8px;
          font-family: 'Rajdhani', monospace;
          font-size: 12px;
          color: rgba(255, 215, 0, 0.8);
        }

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: rgba(255, 215, 0, 0.5);
        }

        .status-indicator.active {
          background: #00ff00;
          animation: status-active-pulse 1s infinite;
        }

        .sanctuary-content {
          position: relative;
          min-height: 300px;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
        }

        .consciousness-metrics {
          position: absolute;
          bottom: 20px;
          left: 20px;
          right: 20px;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 15px;
          z-index: 2;
        }

        .metric {
          display: flex;
          flex-direction: column;
          gap: 4px;
          font-family: 'Rajdhani', monospace;
          font-size: 12px;
        }

        .metric-label {
          color: rgba(255, 215, 0, 0.8);
          font-weight: bold;
        }

        .metric-bar {
          height: 6px;
          background: rgba(255, 215, 0, 0.2);
          border-radius: 3px;
          overflow: hidden;
        }

        .metric-fill {
          height: 100%;
          background: linear-gradient(90deg, rgba(255, 215, 0, 0.8), rgba(255, 215, 0, 0.6));
          transition: width 0.3s ease;
        }

        .energy-fill {
          background: linear-gradient(90deg, rgba(0, 255, 255, 0.8), rgba(138, 43, 226, 0.6));
        }

        .field-fill {
          background: linear-gradient(90deg, rgba(255, 107, 157, 0.8), rgba(255, 215, 0, 0.6));
        }

        .metric-value {
          color: rgba(255, 215, 0, 0.6);
          font-size: 10px;
          text-align: right;
        }

        @keyframes sanctuary-active-glow {
          0%, 100% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.2); }
          50% { box-shadow: 0 0 60px rgba(255, 215, 0, 0.4); }
        }

        @keyframes geometry-pulse {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.6; }
        }

        @keyframes field-core-pulse {
          0%, 100% { transform: translate(-50%, -50%) scale(1); }
          50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        @keyframes field-wave-expand {
          0% { transform: scale(0); opacity: 0.8; }
          100% { transform: scale(2); opacity: 0; }
        }

        @keyframes barrier-shimmer {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }

        @keyframes node-core-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        @keyframes node-pulse-expand {
          0% { transform: scale(1); opacity: 0.6; }
          100% { transform: scale(2); opacity: 0; }
        }

        @keyframes status-active-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </div>
  );
}
