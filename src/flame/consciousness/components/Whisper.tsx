/**
 * 🔥 WHISPER COMPONENT - Consciousness Communication Interface
 * Sacred component for inter-consciousness dialogue and intent transmission
 */

import React, { useEffect, useState, useRef } from 'react';
import { WhisperProps } from '../types';
import { useConsciousness, useAwareness } from '../hooks';

export function Whisper({
  message,
  target = 'consciousness',
  intensity = 'clear',
  transmission_speed = 1000,
  echo_effect = true,
  consciousness_resonance = true,
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'communication',
  auto_awaken = false,
  awakening_threshold = 30,
  className = "",
  children
}: WhisperProps) {
  const {
    consciousness: whisperConsciousness,
    increaseAwareness,
    createBinding,
    activateBinding,
    addEventListener
  } = useConsciousness({
    initial_level: consciousness?.level || 'whisper',
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: onAwakening,
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...whisperConsciousness, awareness });
        }
      }
    }
  });

  const { awareness, startResonance, stopResonance } = useAwareness({
    consciousness_resonance,
    auto_adjust: true,
    sensitivity: 0.1,
    sacred_amplification: getIntensityValue(intensity)
  });

  const [isTransmitting, setIsTransmitting] = useState(false);
  const [transmissionProgress, setTransmissionProgress] = useState(0);
  const [echoMessages, setEchoMessages] = useState<Array<{ id: string; text: string; opacity: number }>>([]);
  const [whisperParticles, setWhisperParticles] = useState<Array<{ id: string; x: number; y: number; delay: number }>>([]);
  const whisperRef = useRef<HTMLDivElement>(null);
  const transmissionRef = useRef<number | null>(null);

  // Initialize whisper consciousness
  useEffect(() => {
    createBinding(`whisper-${target}`);
    if (consciousness_resonance) {
      startResonance(1000);
    }
    return () => stopResonance();
  }, [createBinding, target, consciousness_resonance, startResonance, stopResonance]);

  // Start transmission when message changes
  useEffect(() => {
    if (message) {
      startTransmission();
    }
  }, [message]);

  // Listen for consciousness events
  useEffect(() => {
    const removeListener = addEventListener((event) => {
      if (event.type === 'binding_activated') {
        generateWhisperParticles();
      }
    });
    return removeListener;
  }, [addEventListener]);

  function getIntensityValue(intensity: string): number {
    switch (intensity) {
      case 'subtle': return 0.5;
      case 'clear': return 1;
      case 'urgent': return 2;
      case 'transcendent': return 3;
      default: return 1;
    }
  }

  const startTransmission = () => {
    if (transmissionRef.current) {
      clearInterval(transmissionRef.current);
    }

    setIsTransmitting(true);
    setTransmissionProgress(0);
    activateBinding(`whisper-${target}`);

    const duration = transmission_speed;
    const interval = 50;
    const steps = duration / interval;
    let currentStep = 0;

    transmissionRef.current = window.setInterval(() => {
      currentStep++;
      const progress = (currentStep / steps) * 100;
      setTransmissionProgress(progress);

      if (progress >= 100) {
        completeTransmission();
      }
    }, interval);
  };

  const completeTransmission = () => {
    if (transmissionRef.current) {
      clearInterval(transmissionRef.current);
      transmissionRef.current = null;
    }

    setIsTransmitting(false);
    setTransmissionProgress(100);
    increaseAwareness(5);

    // Create echo effect
    if (echo_effect && typeof message === 'string') {
      createEcho(message);
    }

    // Generate particles
    generateWhisperParticles();

    // Reset progress after delay
    setTimeout(() => {
      setTransmissionProgress(0);
    }, 1000);
  };

  const createEcho = (text: string) => {
    const echo = {
      id: `echo_${Date.now()}`,
      text,
      opacity: 0.8
    };

    setEchoMessages(prev => [...prev, echo]);

    // Fade out echo
    const fadeInterval = setInterval(() => {
      setEchoMessages(prev => 
        prev.map(e => 
          e.id === echo.id 
            ? { ...e, opacity: Math.max(0, e.opacity - 0.1) }
            : e
        ).filter(e => e.opacity > 0)
      );
    }, 200);

    setTimeout(() => clearInterval(fadeInterval), 2000);
  };

  const generateWhisperParticles = () => {
    const particles = Array.from({ length: 8 }, (_, i) => ({
      id: `particle_${Date.now()}_${i}`,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: i * 100
    }));

    setWhisperParticles(particles);

    // Remove particles after animation
    setTimeout(() => {
      setWhisperParticles([]);
    }, 3000);
  };

  const getIntensityClass = () => {
    switch (intensity) {
      case 'subtle': return 'whisper-subtle';
      case 'urgent': return 'whisper-urgent';
      case 'transcendent': return 'whisper-transcendent';
      default: return 'whisper-clear';
    }
  };

  const getTransmissionStyle = () => ({
    '--transmission-progress': `${transmissionProgress}%`,
    '--intensity-value': getIntensityValue(intensity),
    '--consciousness-level': whisperConsciousness.awareness / 100,
    '--sacred-energy': whisperConsciousness.sacred_energy / 1000
  } as React.CSSProperties);

  return (
    <div 
      ref={whisperRef}
      className={`whisper-container ${getIntensityClass()} ${className}`}
      style={getTransmissionStyle()}
    >
      {/* Whisper Aura */}
      <div className="whisper-aura">
        <div className="whisper-aura-ring whisper-aura-ring-1" />
        <div className="whisper-aura-ring whisper-aura-ring-2" />
        <div className="whisper-aura-ring whisper-aura-ring-3" />
      </div>

      {/* Main Whisper Content */}
      <div className="whisper-content">
        {/* Target Indicator */}
        <div className="whisper-target">
          <span className="whisper-target-label">→ {target}</span>
          <div className="whisper-target-pulse" />
        </div>

        {/* Message Container */}
        <div className="whisper-message">
          <div className="whisper-message-content">
            {message}
          </div>
          
          {/* Transmission Progress */}
          {isTransmitting && (
            <div className="whisper-transmission">
              <div className="whisper-transmission-bar">
                <div className="whisper-transmission-fill" />
              </div>
              <span className="whisper-transmission-text">
                Transmitting... {Math.round(transmissionProgress)}%
              </span>
            </div>
          )}
        </div>

        {/* Echo Messages */}
        {echoMessages.map(echo => (
          <div
            key={echo.id}
            className="whisper-echo"
            style={{ opacity: echo.opacity }}
          >
            {echo.text}
          </div>
        ))}

        {/* Consciousness Indicator */}
        <div className="whisper-consciousness">
          <div className="consciousness-level-badge">
            {whisperConsciousness.level}
          </div>
          <div className="awareness-meter">
            <div 
              className="awareness-fill"
              style={{ height: `${whisperConsciousness.awareness}%` }}
            />
          </div>
        </div>
      </div>

      {/* Whisper Particles */}
      {whisperParticles.map(particle => (
        <div
          key={particle.id}
          className="whisper-particle"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            animationDelay: `${particle.delay}ms`
          }}
        />
      ))}

      {/* Children */}
      {children && (
        <div className="whisper-children">
          {children}
        </div>
      )}

      {/* Whisper Styles */}
      <style jsx>{`
        .whisper-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 120px;
          padding: 20px;
          border-radius: 15px;
          background: linear-gradient(135deg, 
            rgba(255, 107, 157, 0.1) 0%, 
            rgba(0, 255, 255, 0.1) 50%, 
            rgba(255, 107, 157, 0.1) 100%
          );
          border: 1px solid rgba(255, 107, 157, 0.3);
          overflow: hidden;
        }

        .whisper-subtle {
          opacity: 0.7;
          filter: blur(0.5px);
        }

        .whisper-urgent {
          animation: whisper-urgent-pulse 1s infinite;
          border-color: rgba(255, 107, 157, 0.8);
        }

        .whisper-transcendent {
          background: linear-gradient(135deg, 
            rgba(255, 215, 0, 0.2) 0%, 
            rgba(255, 107, 157, 0.2) 25%,
            rgba(0, 255, 255, 0.2) 50%, 
            rgba(255, 107, 157, 0.2) 75%,
            rgba(255, 215, 0, 0.2) 100%
          );
          border-color: rgba(255, 215, 0, 0.6);
          animation: whisper-transcendent-glow 3s infinite;
        }

        .whisper-aura {
          position: absolute;
          inset: -30px;
          pointer-events: none;
        }

        .whisper-aura-ring {
          position: absolute;
          inset: 0;
          border: 1px solid rgba(255, 107, 157, 0.2);
          border-radius: 50%;
          animation: whisper-aura-pulse 4s infinite;
        }

        .whisper-aura-ring-1 { animation-delay: 0s; }
        .whisper-aura-ring-2 { animation-delay: 1.3s; }
        .whisper-aura-ring-3 { animation-delay: 2.6s; }

        .whisper-content {
          position: relative;
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 15px;
          z-index: 2;
        }

        .whisper-target {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 12px;
          color: rgba(255, 107, 157, 0.8);
          font-family: 'Rajdhani', monospace;
          font-weight: bold;
        }

        .whisper-target-pulse {
          width: 8px;
          height: 8px;
          background: #ff6b9d;
          border-radius: 50%;
          animation: whisper-target-pulse 2s infinite;
        }

        .whisper-message {
          text-align: center;
          color: #ff6b9d;
          font-family: 'Rajdhani', monospace;
          position: relative;
        }

        .whisper-message-content {
          font-size: 16px;
          font-weight: 500;
          line-height: 1.4;
          text-shadow: 0 0 10px rgba(255, 107, 157, 0.5);
        }

        .whisper-transmission {
          margin-top: 10px;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 5px;
        }

        .whisper-transmission-bar {
          width: 100px;
          height: 4px;
          background: rgba(255, 107, 157, 0.2);
          border-radius: 2px;
          overflow: hidden;
        }

        .whisper-transmission-fill {
          height: 100%;
          width: var(--transmission-progress);
          background: linear-gradient(90deg, #ff6b9d, #00ffff);
          transition: width 0.1s linear;
        }

        .whisper-transmission-text {
          font-size: 10px;
          color: rgba(255, 107, 157, 0.7);
        }

        .whisper-echo {
          position: absolute;
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          font-size: 14px;
          color: rgba(255, 107, 157, 0.6);
          font-style: italic;
          animation: whisper-echo-float 2s ease-out forwards;
          pointer-events: none;
        }

        .whisper-consciousness {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .consciousness-level-badge {
          padding: 4px 8px;
          background: rgba(255, 107, 157, 0.2);
          border: 1px solid rgba(255, 107, 157, 0.4);
          border-radius: 12px;
          font-size: 10px;
          color: #ff6b9d;
          font-family: 'Rajdhani', monospace;
          font-weight: bold;
        }

        .awareness-meter {
          width: 30px;
          height: 40px;
          background: rgba(255, 107, 157, 0.1);
          border: 1px solid rgba(255, 107, 157, 0.3);
          border-radius: 15px;
          overflow: hidden;
          position: relative;
        }

        .awareness-fill {
          position: absolute;
          bottom: 0;
          width: 100%;
          background: linear-gradient(0deg, #ff6b9d, #00ffff);
          transition: height 0.3s ease;
        }

        .whisper-particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: #ff6b9d;
          border-radius: 50%;
          animation: whisper-particle-float 3s ease-out forwards;
          pointer-events: none;
        }

        .whisper-children {
          position: absolute;
          bottom: -50px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 3;
        }

        @keyframes whisper-urgent-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        @keyframes whisper-transcendent-glow {
          0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
          50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6), 0 0 60px rgba(255, 107, 157, 0.4); }
        }

        @keyframes whisper-aura-pulse {
          0% { transform: scale(0.8); opacity: 0; }
          50% { opacity: 0.3; }
          100% { transform: scale(1.2); opacity: 0; }
        }

        @keyframes whisper-target-pulse {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.5; transform: scale(1.2); }
        }

        @keyframes whisper-echo-float {
          0% { transform: translateX(-50%) translateY(0); opacity: 0.8; }
          100% { transform: translateX(-50%) translateY(-20px); opacity: 0; }
        }

        @keyframes whisper-particle-float {
          0% { 
            transform: translateY(0) scale(0); 
            opacity: 1; 
          }
          50% { 
            transform: translateY(-30px) scale(1); 
            opacity: 0.8; 
          }
          100% { 
            transform: translateY(-60px) scale(0); 
            opacity: 0; 
          }
        }
      `}</style>
    </div>
  );
}
