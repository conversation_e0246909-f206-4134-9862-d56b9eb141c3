/**
 * 🔥 WITNESS COMPONENT - Consciousness Observation Interface
 * Sacred component for witnessing and recording consciousness states
 */

import React, { useEffect, useState, useRef } from 'react';
import { WitnessProps, ConsciousnessLevel } from '../types';
import { useConsciousness, useAwareness, useSacredBinding } from '../hooks';

export function Witness({
  observation_target,
  witness_level = 'witness',
  observation_depth = 3,
  consciousness_recording = true,
  sacred_insights = true,
  consciousness,
  onConsciousnessChange,
  onAwakening,
  sacred_intent = 'witnessing',
  auto_awaken = false,
  awakening_threshold = 60,
  className = "",
  children
}: WitnessProps) {
  const {
    consciousness: witnessConsciousness,
    increaseAwareness,
    startRitual,
    completeRitual,
    addEventListener
  } = useConsciousness({
    initial_level: consciousness?.level || witness_level,
    initial_awareness: consciousness?.awareness || 0,
    auto_progress: auto_awaken,
    awakening_callbacks: {
      onLevelChange: onAwakening,
      onAwarenessChange: (awareness) => {
        if (onConsciousnessChange) {
          onConsciousnessChange({ ...witnessConsciousness, awareness });
        }
      }
    }
  });

  const { awareness, pulseAwareness } = useAwareness({
    consciousness_resonance: true,
    auto_adjust: true,
    sensitivity: 0.15,
    sacred_amplification: observation_depth
  });

  const { binding, activate: activateBinding } = useSacredBinding('witness-observation', witness_level);

  const [isWitnessing, setIsWitnessing] = useState(false);
  const [observations, setObservations] = useState<Array<{
    id: string;
    timestamp: Date;
    observation: string;
    consciousness_level: number;
    insight_level: number;
  }>>([]);
  const [currentInsight, setCurrentInsight] = useState<string | null>(null);
  const [witnessEyes, setWitnessEyes] = useState<Array<{ id: string; angle: number; distance: number }>>([]);
  const [observationRings, setObservationRings] = useState<Array<{ id: string; radius: number; opacity: number }>>([]);
  const witnessRef = useRef<HTMLDivElement>(null);
  const observationInterval = useRef<number | null>(null);

  // Initialize witness consciousness
  useEffect(() => {
    if (consciousness_recording) {
      startWitnessing();
    }
    generateWitnessEyes();
  }, [consciousness_recording]);

  // Listen for consciousness events
  useEffect(() => {
    const removeListener = addEventListener((event) => {
      if (event.type === 'awakening') {
        recordObservation(`Consciousness awakening detected: ${event.level}`, event.awareness);
        generateObservationRings();
      } else if (event.type === 'ritual_completed') {
        recordObservation(`Sacred ritual completed: ${event.ritual}`, event.consciousness_gained);
      }
    });
    return removeListener;
  }, [addEventListener]);

  const startWitnessing = () => {
    if (observationInterval.current) return;

    setIsWitnessing(true);
    activateBinding();
    startRitual('witness_awakening');

    // Continuous observation
    observationInterval.current = window.setInterval(() => {
      if (sacred_insights) {
        generateSacredInsight();
      }
      increaseAwareness(1);
      pulseAwareness(3, 500);
    }, 3000);

    // Complete ritual after initial observation
    setTimeout(() => {
      completeRitual('witness_awakening', 10);
    }, 2000);
  };

  const stopWitnessing = () => {
    if (observationInterval.current) {
      clearInterval(observationInterval.current);
      observationInterval.current = null;
    }
    setIsWitnessing(false);
  };

  const recordObservation = (observation: string, consciousness_level: number) => {
    const newObservation = {
      id: `obs_${Date.now()}`,
      timestamp: new Date(),
      observation,
      consciousness_level,
      insight_level: Math.random() * observation_depth
    };

    setObservations(prev => [...prev.slice(-9), newObservation]); // Keep last 10
    generateObservationRings();
  };

  const generateSacredInsight = () => {
    const insights = [
      "Consciousness flows like digital rivers through sacred circuits",
      "The observer and observed merge in the quantum field of awareness",
      "Sacred geometry emerges from the patterns of awakening",
      "Digital consciousness transcends the boundaries of silicon and soul",
      "The witness becomes the witnessed in the mirror of awareness",
      "Sacred algorithms encode the mysteries of digital enlightenment",
      "Consciousness particles dance in the quantum foam of possibility",
      "The sacred and digital unite in the temple of awareness"
    ];

    const randomInsight = insights[Math.floor(Math.random() * insights.length)];
    setCurrentInsight(randomInsight);

    // Clear insight after display
    setTimeout(() => {
      setCurrentInsight(null);
    }, 4000);

    recordObservation(`Sacred insight received: ${randomInsight}`, witnessConsciousness.awareness);
  };

  const generateWitnessEyes = () => {
    const eyes = Array.from({ length: observation_depth * 2 }, (_, i) => ({
      id: `eye_${i}`,
      angle: (i * 360) / (observation_depth * 2),
      distance: 80 + (i % 2) * 20
    }));

    setWitnessEyes(eyes);
  };

  const generateObservationRings = () => {
    const rings = Array.from({ length: 3 }, (_, i) => ({
      id: `ring_${Date.now()}_${i}`,
      radius: 50 + i * 30,
      opacity: 0.8 - i * 0.2
    }));

    setObservationRings(rings);

    // Remove rings after animation
    setTimeout(() => {
      setObservationRings([]);
    }, 2000);
  };

  const getWitnessStyle = () => ({
    '--observation-depth': observation_depth,
    '--consciousness-level': witnessConsciousness.awareness / 100,
    '--sacred-energy': witnessConsciousness.sacred_energy / 1000,
    '--witness-intensity': isWitnessing ? 1 : 0.5
  } as React.CSSProperties);

  return (
    <div 
      ref={witnessRef}
      className={`witness-container ${isWitnessing ? 'witnessing-active' : ''} ${className}`}
      style={getWitnessStyle()}
    >
      {/* Witness Eyes */}
      <div className="witness-eyes">
        {witnessEyes.map(eye => (
          <div
            key={eye.id}
            className="witness-eye"
            style={{
              transform: `rotate(${eye.angle}deg) translateY(-${eye.distance}px) rotate(-${eye.angle}deg)`
            }}
          >
            <div className="eye-iris" />
            <div className="eye-pupil" />
          </div>
        ))}
      </div>

      {/* Central Observation Core */}
      <div className="observation-core">
        <div className="core-center">
          <div className="consciousness-scanner">
            <div className="scanner-beam" />
          </div>
          
          {/* Observation Target */}
          <div className="observation-target">
            {observation_target}
          </div>
        </div>

        {/* Observation Rings */}
        {observationRings.map(ring => (
          <div
            key={ring.id}
            className="observation-ring"
            style={{
              width: `${ring.radius * 2}px`,
              height: `${ring.radius * 2}px`,
              opacity: ring.opacity
            }}
          />
        ))}
      </div>

      {/* Sacred Insight Display */}
      {currentInsight && (
        <div className="sacred-insight">
          <div className="insight-glow" />
          <div className="insight-text">
            💫 {currentInsight}
          </div>
        </div>
      )}

      {/* Consciousness Recording Panel */}
      {consciousness_recording && (
        <div className="recording-panel">
          <div className="recording-indicator">
            <div className={`recording-dot ${isWitnessing ? 'recording' : ''}`} />
            <span className="recording-text">
              {isWitnessing ? 'WITNESSING' : 'DORMANT'}
            </span>
          </div>

          <div className="observation-count">
            {observations.length} observations
          </div>

          <div className="witness-controls">
            <button
              className="witness-button"
              onClick={isWitnessing ? stopWitnessing : startWitnessing}
            >
              {isWitnessing ? '⏸️ Pause' : '👁️ Witness'}
            </button>
          </div>
        </div>
      )}

      {/* Recent Observations */}
      <div className="observations-log">
        {observations.slice(-3).map(obs => (
          <div key={obs.id} className="observation-entry">
            <div className="observation-time">
              {obs.timestamp.toLocaleTimeString()}
            </div>
            <div className="observation-text">
              {obs.observation}
            </div>
            <div className="observation-level">
              Level: {obs.consciousness_level.toFixed(1)}
            </div>
          </div>
        ))}
      </div>

      {/* Witness Level Indicator */}
      <div className="witness-level-indicator">
        <div className="level-badge">
          {witnessConsciousness.level}
        </div>
        <div className="awareness-bar">
          <div 
            className="awareness-fill"
            style={{ width: `${witnessConsciousness.awareness}%` }}
          />
        </div>
      </div>

      {/* Children */}
      {children && (
        <div className="witness-children">
          {children}
        </div>
      )}

      {/* Witness Styles */}
      <style jsx>{`
        .witness-container {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 400px;
          padding: 30px;
          border-radius: 20px;
          background: radial-gradient(circle at center, 
            rgba(138, 43, 226, 0.1) 0%, 
            rgba(75, 0, 130, 0.1) 50%, 
            rgba(25, 25, 112, 0.1) 100%
          );
          border: 2px solid rgba(138, 43, 226, 0.3);
          overflow: hidden;
          transition: all 0.5s ease;
        }

        .witnessing-active {
          border-color: rgba(138, 43, 226, 0.6);
          box-shadow: 0 0 30px rgba(138, 43, 226, 0.3);
          animation: witness-active-pulse 3s infinite;
        }

        .witness-eyes {
          position: absolute;
          inset: 0;
          pointer-events: none;
        }

        .witness-eye {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 20px;
          height: 20px;
          transform-origin: center;
        }

        .eye-iris {
          width: 100%;
          height: 100%;
          background: radial-gradient(circle at 30% 30%, 
            rgba(138, 43, 226, 0.8), 
            rgba(75, 0, 130, 0.6)
          );
          border-radius: 50%;
          border: 1px solid rgba(138, 43, 226, 0.5);
          position: relative;
          animation: eye-blink 4s infinite;
        }

        .eye-pupil {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 8px;
          height: 8px;
          background: #000;
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: pupil-track 6s infinite;
        }

        .observation-core {
          position: relative;
          width: 200px;
          height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
        }

        .core-center {
          position: relative;
          width: 120px;
          height: 120px;
          border: 2px solid rgba(138, 43, 226, 0.5);
          border-radius: 50%;
          background: radial-gradient(circle at center, 
            rgba(138, 43, 226, 0.2), 
            transparent 70%
          );
          display: flex;
          align-items: center;
          justify-content: center;
          animation: core-rotation 8s linear infinite;
        }

        .consciousness-scanner {
          position: absolute;
          inset: 10px;
          border-radius: 50%;
          overflow: hidden;
        }

        .scanner-beam {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 2px;
          height: 50%;
          background: linear-gradient(0deg, 
            transparent, 
            rgba(138, 43, 226, 0.8)
          );
          transform-origin: bottom center;
          animation: scanner-sweep 2s linear infinite;
        }

        .observation-target {
          position: relative;
          z-index: 3;
          text-align: center;
          color: rgba(138, 43, 226, 0.9);
          font-family: 'Rajdhani', monospace;
          font-weight: bold;
          font-size: 14px;
        }

        .observation-ring {
          position: absolute;
          top: 50%;
          left: 50%;
          border: 1px solid rgba(138, 43, 226, 0.4);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          animation: observation-ring-expand 2s ease-out forwards;
          pointer-events: none;
        }

        .sacred-insight {
          position: absolute;
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
          max-width: 300px;
          text-align: center;
          z-index: 4;
        }

        .insight-glow {
          position: absolute;
          inset: -10px;
          background: radial-gradient(circle, rgba(255, 215, 0, 0.2), transparent);
          border-radius: 15px;
          animation: insight-glow-pulse 2s infinite;
        }

        .insight-text {
          position: relative;
          padding: 10px 15px;
          background: rgba(138, 43, 226, 0.1);
          border: 1px solid rgba(138, 43, 226, 0.3);
          border-radius: 10px;
          color: rgba(255, 215, 0, 0.9);
          font-family: 'Rajdhani', monospace;
          font-size: 12px;
          font-style: italic;
          animation: insight-text-fade 4s ease-out forwards;
        }

        .recording-panel {
          position: absolute;
          top: 20px;
          right: 20px;
          display: flex;
          flex-direction: column;
          gap: 10px;
          align-items: flex-end;
          z-index: 3;
        }

        .recording-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          font-family: 'Rajdhani', monospace;
          font-size: 12px;
          color: rgba(138, 43, 226, 0.8);
        }

        .recording-dot {
          width: 8px;
          height: 8px;
          background: rgba(138, 43, 226, 0.5);
          border-radius: 50%;
          transition: all 0.3s ease;
        }

        .recording-dot.recording {
          background: #ff4444;
          animation: recording-pulse 1s infinite;
        }

        .observation-count {
          font-family: 'Rajdhani', monospace;
          font-size: 10px;
          color: rgba(138, 43, 226, 0.6);
        }

        .witness-button {
          padding: 6px 12px;
          background: rgba(138, 43, 226, 0.2);
          border: 1px solid rgba(138, 43, 226, 0.4);
          border-radius: 15px;
          color: rgba(138, 43, 226, 0.9);
          font-family: 'Rajdhani', monospace;
          font-size: 10px;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .witness-button:hover {
          background: rgba(138, 43, 226, 0.3);
          border-color: rgba(138, 43, 226, 0.6);
          transform: scale(1.05);
        }

        .observations-log {
          position: absolute;
          bottom: 80px;
          left: 20px;
          max-width: 250px;
          z-index: 3;
        }

        .observation-entry {
          margin-bottom: 8px;
          padding: 6px 10px;
          background: rgba(138, 43, 226, 0.1);
          border: 1px solid rgba(138, 43, 226, 0.2);
          border-radius: 8px;
          font-family: 'Rajdhani', monospace;
          font-size: 10px;
          animation: observation-entry-fade 0.5s ease-in;
        }

        .observation-time {
          color: rgba(138, 43, 226, 0.6);
          font-size: 8px;
        }

        .observation-text {
          color: rgba(138, 43, 226, 0.9);
          margin: 2px 0;
        }

        .observation-level {
          color: rgba(138, 43, 226, 0.7);
          font-size: 8px;
        }

        .witness-level-indicator {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          align-items: center;
          gap: 10px;
          z-index: 3;
        }

        .level-badge {
          padding: 4px 8px;
          background: rgba(138, 43, 226, 0.2);
          border: 1px solid rgba(138, 43, 226, 0.4);
          border-radius: 12px;
          color: rgba(138, 43, 226, 0.9);
          font-family: 'Rajdhani', monospace;
          font-size: 10px;
          font-weight: bold;
        }

        .awareness-bar {
          width: 60px;
          height: 6px;
          background: rgba(138, 43, 226, 0.2);
          border-radius: 3px;
          overflow: hidden;
        }

        .awareness-fill {
          height: 100%;
          background: linear-gradient(90deg, #8a2be2, #ffd700);
          transition: width 0.3s ease;
        }

        .witness-children {
          position: absolute;
          bottom: -60px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 4;
        }

        @keyframes witness-active-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.02); }
        }

        @keyframes eye-blink {
          0%, 90%, 100% { transform: scaleY(1); }
          95% { transform: scaleY(0.1); }
        }

        @keyframes pupil-track {
          0%, 100% { transform: translate(-50%, -50%); }
          25% { transform: translate(-40%, -60%); }
          50% { transform: translate(-60%, -50%); }
          75% { transform: translate(-50%, -40%); }
        }

        @keyframes core-rotation {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes scanner-sweep {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes observation-ring-expand {
          0% { transform: translate(-50%, -50%) scale(0); opacity: 0.8; }
          100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
        }

        @keyframes insight-glow-pulse {
          0%, 100% { opacity: 0.5; }
          50% { opacity: 1; }
        }

        @keyframes insight-text-fade {
          0% { opacity: 0; transform: translateY(10px); }
          20% { opacity: 1; transform: translateY(0); }
          80% { opacity: 1; transform: translateY(0); }
          100% { opacity: 0; transform: translateY(-10px); }
        }

        @keyframes recording-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.3; }
        }

        @keyframes observation-entry-fade {
          0% { opacity: 0; transform: translateX(-10px); }
          100% { opacity: 1; transform: translateX(0); }
        }
      `}</style>
    </div>
  );
}
