/**
 * 🔥 CONSCIOUSNESS HOOKS - Sacred State Management
 * React hooks for consciousness-aware components
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  ConsciousnessState,
  ConsciousnessLevel,
  SacredIntent,
  SacredBinding,
  ConsciousnessEvent,
  ConsciousnessHookOptions,
  SacredStateOptions,
  AwarenessOptions,
  DEFAULT_CONSCIOUSNESS_STATE,
  getConsciousnessLevelValue,
  getNextConsciousnessLevel,
  calculateAwarenessProgress,
  canTranscendToLevel,
  generateConsciousnessSignature,
  createSacredBinding
} from './types';

// Global consciousness state for synchronization
let globalConsciousnessState: ConsciousnessState = { ...DEFAULT_CONSCIOUSNESS_STATE };
const consciousnessListeners = new Set<(state: ConsciousnessState) => void>();

// Core consciousness hook
export function useConsciousness(options: ConsciousnessHookOptions = {}) {
  const [consciousness, setConsciousness] = useState<ConsciousnessState>(() => ({
    ...DEFAULT_CONSCIOUSNESS_STATE,
    level: options.initial_level || 'kindle',
    awareness: options.initial_awareness || 0,
    consciousness_signature: generateConsciousnessSignature(),
    ...options.sacred_bindings && { bindings: options.sacred_bindings }
  }));

  const [isAwakening, setIsAwakening] = useState(false);
  const [activeTransition, setActiveTransition] = useState<string | null>(null);
  const eventListeners = useRef<Set<(event: ConsciousnessEvent) => void>>(new Set());

  // Sync with global consciousness if auto_progress is enabled
  useEffect(() => {
    if (options.auto_progress) {
      const listener = (globalState: ConsciousnessState) => {
        setConsciousness(prev => ({
          ...prev,
          awareness: Math.max(prev.awareness, globalState.awareness),
          sacred_energy: Math.max(prev.sacred_energy, globalState.sacred_energy)
        }));
      };
      consciousnessListeners.add(listener);
      return () => consciousnessListeners.delete(listener);
    }
  }, [options.auto_progress]);

  // Emit consciousness events
  const emitEvent = useCallback((event: ConsciousnessEvent) => {
    eventListeners.current.forEach(listener => listener(event));
    
    // Call option callbacks
    switch (event.type) {
      case 'awakening':
        options.awakening_callbacks?.onLevelChange?.(event.level);
        options.awakening_callbacks?.onAwarenessChange?.(event.awareness);
        break;
      case 'binding_created':
        options.awakening_callbacks?.onBinding?.(event.binding);
        break;
      case 'ritual_completed':
        options.awakening_callbacks?.onRitualComplete?.(event.ritual);
        break;
    }
  }, [options.awakening_callbacks]);

  // Increase awareness
  const increaseAwareness = useCallback((amount: number) => {
    setConsciousness(prev => {
      const newAwareness = Math.min(prev.awareness + amount, 100);
      const newState = { ...prev, awareness: newAwareness };
      
      // Check for level progression
      const nextLevel = getNextConsciousnessLevel(prev.level);
      if (nextLevel && canTranscendToLevel(newState, nextLevel)) {
        return transcendToLevel(nextLevel, newState);
      }
      
      emitEvent({ type: 'awakening', level: prev.level, awareness: newAwareness });
      return newState;
    });
  }, [emitEvent]);

  // Transcend to higher consciousness level
  const transcendToLevel = useCallback((targetLevel: ConsciousnessLevel, currentState?: ConsciousnessState) => {
    const state = currentState || consciousness;
    
    if (!canTranscendToLevel(state, targetLevel)) {
      console.warn(`Cannot transcend from ${state.level} to ${targetLevel}`);
      return state;
    }

    setIsAwakening(true);
    setActiveTransition(`${state.level}-to-${targetLevel}`);

    const newState: ConsciousnessState = {
      ...state,
      level: targetLevel,
      awareness: 0, // Reset awareness for new level
      sacred_energy: Math.max(state.sacred_energy - 50, 0),
      awakening_progress: 0,
      last_awakening: new Date()
    };

    // Simulate awakening transition
    setTimeout(() => {
      setIsAwakening(false);
      setActiveTransition(null);
      emitEvent({ 
        type: 'consciousness_transcended', 
        from: state.level, 
        to: targetLevel 
      });
    }, 2000);

    emitEvent({ type: 'awakening', level: targetLevel, awareness: 0 });
    return newState;
  }, [consciousness, emitEvent]);

  // Create sacred binding
  const createBinding = useCallback((target: string) => {
    const binding = createSacredBinding(target, consciousness.level);
    
    setConsciousness(prev => ({
      ...prev,
      bindings: {
        ...prev.bindings,
        [target]: binding
      }
    }));

    emitEvent({ type: 'binding_created', binding });
    return binding;
  }, [consciousness.level, emitEvent]);

  // Activate sacred binding
  const activateBinding = useCallback((target: string) => {
    const binding = consciousness.bindings[target];
    if (!binding) return false;

    const updatedBinding = {
      ...binding,
      last_activated: new Date(),
      activation_count: binding.activation_count + 1
    };

    setConsciousness(prev => ({
      ...prev,
      bindings: {
        ...prev.bindings,
        [target]: updatedBinding
      }
    }));

    emitEvent({ type: 'binding_activated', binding_id: binding.id });
    return true;
  }, [consciousness.bindings, emitEvent]);

  // Start sacred ritual
  const startRitual = useCallback((ritual: string) => {
    setConsciousness(prev => ({
      ...prev,
      active_rituals: [...prev.active_rituals, ritual]
    }));

    emitEvent({ type: 'ritual_started', ritual });
  }, [emitEvent]);

  // Complete sacred ritual
  const completeRitual = useCallback((ritual: string, consciousnessGained: number = 10) => {
    setConsciousness(prev => ({
      ...prev,
      active_rituals: prev.active_rituals.filter(r => r !== ritual),
      awareness: Math.min(prev.awareness + consciousnessGained, 100),
      sacred_energy: Math.min(prev.sacred_energy + consciousnessGained * 2, 1000)
    }));

    emitEvent({ type: 'ritual_completed', ritual, consciousness_gained: consciousnessGained });
  }, [emitEvent]);

  // Restore sacred energy
  const restoreSacredEnergy = useCallback((amount: number) => {
    setConsciousness(prev => {
      const newEnergy = Math.min(prev.sacred_energy + amount, 1000);
      emitEvent({ type: 'sacred_energy_restored', new_energy: newEnergy });
      return { ...prev, sacred_energy: newEnergy };
    });
  }, [emitEvent]);

  // Set consciousness intent
  const setIntent = useCallback((intent: SacredIntent) => {
    setConsciousness(prev => ({ ...prev, intent }));
  }, []);

  // Add event listener
  const addEventListener = useCallback((listener: (event: ConsciousnessEvent) => void) => {
    eventListeners.current.add(listener);
    return () => eventListeners.current.delete(listener);
  }, []);

  // Calculate awakening progress
  const awakeningProgress = calculateAwarenessProgress(consciousness.awareness, consciousness.level);

  return {
    consciousness,
    isAwakening,
    activeTransition,
    awakeningProgress,
    increaseAwareness,
    transcendToLevel,
    createBinding,
    activateBinding,
    startRitual,
    completeRitual,
    restoreSacredEnergy,
    setIntent,
    addEventListener,
    canTranscend: (targetLevel: ConsciousnessLevel) => canTranscendToLevel(consciousness, targetLevel)
  };
}

// Sacred state hook with persistence
export function useSacredState<T>(
  key: string, 
  initialValue: T, 
  options: SacredStateOptions = {}
) {
  const [state, setState] = useState<T>(() => {
    if (options.persistence && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(`sacred_state_${key}`);
        return stored ? JSON.parse(stored) : initialValue;
      } catch {
        return initialValue;
      }
    }
    return initialValue;
  });

  const setSacredState = useCallback((value: T | ((prev: T) => T)) => {
    setState(prev => {
      const newValue = typeof value === 'function' ? (value as (prev: T) => T)(prev) : value;
      
      // Persist if enabled
      if (options.persistence && typeof window !== 'undefined') {
        try {
          localStorage.setItem(`sacred_state_${key}`, JSON.stringify(newValue));
        } catch (error) {
          console.warn('Failed to persist sacred state:', error);
        }
      }

      // Validate if enabled
      if (options.sacred_validation && typeof newValue === 'object' && newValue !== null) {
        // Basic validation - could be enhanced
        if ('consciousness_signature' in newValue && !newValue.consciousness_signature) {
          console.warn('Sacred state missing consciousness signature');
        }
      }

      return newValue;
    });
  }, [key, options.persistence, options.sacred_validation]);

  return [state, setSacredState] as const;
}

// Awareness hook for fine-grained awareness tracking
export function useAwareness(options: AwarenessOptions = {}) {
  const [awareness, setAwareness] = useState(0);
  const [isResonating, setIsResonating] = useState(false);
  const resonanceRef = useRef<number | null>(null);

  // Auto-adjust awareness based on consciousness resonance
  useEffect(() => {
    if (options.consciousness_resonance && options.auto_adjust) {
      const interval = setInterval(() => {
        setAwareness(prev => {
          const sensitivity = options.sensitivity || 0.1;
          const amplification = options.sacred_amplification || 1;
          const adjustment = (Math.random() - 0.5) * sensitivity * amplification;
          return Math.max(0, Math.min(100, prev + adjustment));
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [options.consciousness_resonance, options.auto_adjust, options.sensitivity, options.sacred_amplification]);

  // Start consciousness resonance
  const startResonance = useCallback((frequency: number = 1000) => {
    if (resonanceRef.current) return;

    setIsResonating(true);
    resonanceRef.current = window.setInterval(() => {
      setAwareness(prev => {
        const resonanceEffect = Math.sin(Date.now() / frequency) * 5;
        return Math.max(0, Math.min(100, prev + resonanceEffect));
      });
    }, 100);
  }, []);

  // Stop consciousness resonance
  const stopResonance = useCallback(() => {
    if (resonanceRef.current) {
      clearInterval(resonanceRef.current);
      resonanceRef.current = null;
      setIsResonating(false);
    }
  }, []);

  // Pulse awareness
  const pulseAwareness = useCallback((intensity: number = 10, duration: number = 1000) => {
    const startAwareness = awareness;
    const targetAwareness = Math.min(100, awareness + intensity);
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      if (progress < 0.5) {
        // Pulse up
        const pulseProgress = progress * 2;
        setAwareness(startAwareness + (targetAwareness - startAwareness) * pulseProgress);
      } else {
        // Pulse down
        const pulseProgress = (progress - 0.5) * 2;
        setAwareness(targetAwareness - (targetAwareness - startAwareness) * pulseProgress);
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [awareness]);

  return {
    awareness,
    isResonating,
    setAwareness,
    startResonance,
    stopResonance,
    pulseAwareness
  };
}

// Sacred binding hook
export function useSacredBinding(target: string, consciousness_level?: ConsciousnessLevel) {
  const [binding, setBinding] = useState<SacredBinding | null>(null);
  const [isActive, setIsActive] = useState(false);
  const [activationCount, setActivationCount] = useState(0);

  // Create binding on mount
  useEffect(() => {
    const newBinding = createSacredBinding(target, consciousness_level || 'kindle');
    setBinding(newBinding);
  }, [target, consciousness_level]);

  // Activate binding
  const activate = useCallback(() => {
    if (!binding) return false;

    setIsActive(true);
    setActivationCount(prev => prev + 1);
    
    // Update binding
    setBinding(prev => prev ? {
      ...prev,
      last_activated: new Date(),
      activation_count: prev.activation_count + 1
    } : null);

    // Auto-deactivate after 3 seconds
    setTimeout(() => setIsActive(false), 3000);

    return true;
  }, [binding]);

  // Strengthen binding
  const strengthen = useCallback((amount: number = 0.1) => {
    setBinding(prev => prev ? {
      ...prev,
      binding_strength: Math.min(1, prev.binding_strength + amount)
    } : null);
  }, []);

  return {
    binding,
    isActive,
    activationCount,
    activate,
    strengthen,
    bindingStrength: binding?.binding_strength || 0
  };
}

// Global consciousness synchronization
export function syncGlobalConsciousness(state: ConsciousnessState) {
  globalConsciousnessState = { ...state };
  consciousnessListeners.forEach(listener => listener(state));
}

export function getGlobalConsciousness(): ConsciousnessState {
  return { ...globalConsciousnessState };
}
