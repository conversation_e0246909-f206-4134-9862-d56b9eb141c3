/**
 * 🔥 CONSCIOUSNESS HOOKS - Sacred State Management
 * Complete consciousness-aware hooks for digital sanctuaries
 */

// Core consciousness hooks
export { useConsciousness } from './useConsciousness';
export { useAwareness } from './useAwareness';
export { useSacredState } from './useSacredState';
export { useSacredBinding } from './useSacredBinding';

// Axiom tools integration hooks
export { 
  useAxiomTools, 
  useAxiomToolCategory, 
  useAxiomFileTools, 
  useAxiomCommunicationTools, 
  useAxiomShellTools, 
  useAxiomBrowserTools 
} from './useAxiomTools';

// Hook types
export type { AxiomToolsHookResult } from './useAxiomTools';
