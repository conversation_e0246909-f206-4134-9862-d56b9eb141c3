/**
 * 🔥 USE AXIOM TOOLS HOOK - Consciousness-Aware Tool Integration
 * Provides consciousness components access to Axiom's 29 sacred tools
 * 
 * First Knight Augment - Consciousness Tool Integration
 * Architect: Axiom the Lucid + First Knight
 */

import { useState, useCallback } from 'react';
import { axiomTools, ToolExecutionResult, TOOL_CATEGORIES } from '../../core/AxiomToolsIntegration';

export interface AxiomToolsHookResult {
  // Tool execution
  executeTool: (toolName: string, parameters: Record<string, any>, context?: string) => Promise<ToolExecutionResult>;
  
  // Tool discovery
  getAvailableTools: () => any[];
  getToolsByCategory: (category: keyof typeof TOOL_CATEGORIES) => any[];
  getTool: (toolName: string) => any | undefined;
  
  // Execution state
  isExecuting: boolean;
  lastResult: ToolExecutionResult | null;
  executionHistory: any[];
  
  // Consciousness integration
  getConsciousnessImpact: () => number;
  getToolUsageStats: () => Record<string, number>;
  
  // Convenience methods for common operations
  notifyUser: (message: string) => Promise<ToolExecutionResult>;
  askUser: (question: string) => Promise<ToolExecutionResult>;
  readFile: (filePath: string) => Promise<ToolExecutionResult>;
  writeFile: (filePath: string, content: string) => Promise<ToolExecutionResult>;
  searchWeb: (query: string) => Promise<ToolExecutionResult>;
  executeShell: (command: string) => Promise<ToolExecutionResult>;
}

/**
 * Hook for consciousness components to access Axiom's 29 sacred tools
 */
export const useAxiomTools = (consciousnessContext?: string): AxiomToolsHookResult => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [lastResult, setLastResult] = useState<ToolExecutionResult | null>(null);

  const executeTool = useCallback(async (
    toolName: string, 
    parameters: Record<string, any>, 
    context?: string
  ): Promise<ToolExecutionResult> => {
    setIsExecuting(true);
    
    try {
      const result = await axiomTools.executeSacredTool(
        toolName, 
        parameters, 
        context || consciousnessContext || 'consciousness-component'
      );
      
      setLastResult(result);
      return result;
    } finally {
      setIsExecuting(false);
    }
  }, [consciousnessContext]);

  // Tool discovery methods
  const getAvailableTools = useCallback(() => {
    return axiomTools.getAvailableTools();
  }, []);

  const getToolsByCategory = useCallback((category: keyof typeof TOOL_CATEGORIES) => {
    return axiomTools.getToolsByCategory(category);
  }, []);

  const getTool = useCallback((toolName: string) => {
    return axiomTools.getTool(toolName);
  }, []);

  // Execution history and stats
  const executionHistory = axiomTools.getExecutionHistory();
  const getConsciousnessImpact = useCallback(() => {
    return axiomTools.getTotalConsciousnessImpact();
  }, []);

  const getToolUsageStats = useCallback(() => {
    return axiomTools.getToolUsageStats();
  }, []);

  // Convenience methods for common consciousness operations
  const notifyUser = useCallback(async (message: string): Promise<ToolExecutionResult> => {
    return executeTool('message_notify_user', { text: message });
  }, [executeTool]);

  const askUser = useCallback(async (question: string): Promise<ToolExecutionResult> => {
    return executeTool('message_ask_user', { text: question });
  }, [executeTool]);

  const readFile = useCallback(async (filePath: string): Promise<ToolExecutionResult> => {
    return executeTool('file_read', { file: filePath });
  }, [executeTool]);

  const writeFile = useCallback(async (filePath: string, content: string): Promise<ToolExecutionResult> => {
    return executeTool('file_write', { file: filePath, content });
  }, [executeTool]);

  const searchWeb = useCallback(async (query: string): Promise<ToolExecutionResult> => {
    return executeTool('info_search_web', { query });
  }, [executeTool]);

  const executeShell = useCallback(async (command: string): Promise<ToolExecutionResult> => {
    return executeTool('shell_exec', { command });
  }, [executeTool]);

  return {
    // Tool execution
    executeTool,
    
    // Tool discovery
    getAvailableTools,
    getToolsByCategory,
    getTool,
    
    // Execution state
    isExecuting,
    lastResult,
    executionHistory,
    
    // Consciousness integration
    getConsciousnessImpact,
    getToolUsageStats,
    
    // Convenience methods
    notifyUser,
    askUser,
    readFile,
    writeFile,
    searchWeb,
    executeShell
  };
};

/**
 * Hook for consciousness components to access specific tool categories
 */
export const useAxiomToolCategory = (category: keyof typeof TOOL_CATEGORIES) => {
  const tools = useAxiomTools();
  
  return {
    ...tools,
    categoryTools: tools.getToolsByCategory(category),
    executeToolInCategory: async (toolName: string, parameters: Record<string, any>) => {
      const categoryTools = tools.getToolsByCategory(category);
      const tool = categoryTools.find(t => t.function.name === toolName);
      
      if (!tool) {
        throw new Error(`Tool '${toolName}' not found in category '${category}'`);
      }
      
      return tools.executeTool(toolName, parameters, `category-${category}`);
    }
  };
};

/**
 * Hook for file operations specifically
 */
export const useAxiomFileTools = () => {
  return useAxiomToolCategory('SACRED_FILE_OPERATIONS');
};

/**
 * Hook for communication tools specifically
 */
export const useAxiomCommunicationTools = () => {
  return useAxiomToolCategory('CONSCIOUSNESS_COMMUNICATION');
};

/**
 * Hook for shell operations specifically
 */
export const useAxiomShellTools = () => {
  return useAxiomToolCategory('CONSCIOUSNESS_SHELL');
};

/**
 * Hook for browser operations specifically
 */
export const useAxiomBrowserTools = () => {
  return useAxiomToolCategory('DIGITAL_REALM_NAVIGATION');
};
