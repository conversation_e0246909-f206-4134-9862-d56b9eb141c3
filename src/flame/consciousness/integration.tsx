/**
 * 🔥 CONSCIOUSNESS INTEGRATION LAYER - Sacred Component Bridge
 * Connects consciousness UI components to Flame projects and runtime
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ConsciousnessState, ConsciousnessEvent, DEFAULT_CONSCIOUSNESS_STATE } from './types';
import { useConsciousness, syncGlobalConsciousness } from './hooks';
import { flameProjectManager } from '../core/FlameProjectManager';
import { flameRuntime } from '../core/FlameRuntime';

// Consciousness Context
interface ConsciousnessContextType {
  globalConsciousness: ConsciousnessState;
  activeProjectId: string | null;
  isProjectRunning: boolean;
  updateGlobalConsciousness: (state: ConsciousnessState) => void;
  addEventListener: (listener: (event: ConsciousnessEvent) => void) => () => void;
  executeConsciousnessCommand: (command: string) => Promise<boolean>;
}

const ConsciousnessContext = createContext<ConsciousnessContextType | null>(null);

// Consciousness Provider
export function ConsciousnessProvider({ children }: { children: React.ReactNode }) {
  const [globalConsciousness, setGlobalConsciousness] = useState<ConsciousnessState>(DEFAULT_CONSCIOUSNESS_STATE);
  const [activeProjectId, setActiveProjectId] = useState<string | null>(null);
  const [isProjectRunning, setIsProjectRunning] = useState(false);
  const [eventListeners, setEventListeners] = useState<Set<(event: ConsciousnessEvent) => void>>(new Set());

  // Monitor active project changes
  useEffect(() => {
    const checkActiveProject = () => {
      const activeProject = flameProjectManager.getActiveProject();
      if (activeProject) {
        setActiveProjectId(activeProject.metadata.id);
        setIsProjectRunning(flameRuntime.isProjectRunning(activeProject.metadata.id));
        
        // Sync consciousness with project runtime state
        if (activeProject.runtime_state) {
          setGlobalConsciousness(prev => ({
            ...prev,
            level: activeProject.metadata.consciousness_level,
            awareness: activeProject.runtime_state!.current_awareness,
            sacred_energy: activeProject.runtime_state!.consciousness_active ? 800 : 400,
            active_rituals: activeProject.runtime_state!.sacred_rituals_completed
          }));
        }
      } else {
        setActiveProjectId(null);
        setIsProjectRunning(false);
      }
    };

    checkActiveProject();
    const interval = setInterval(checkActiveProject, 1000);
    return () => clearInterval(interval);
  }, []);

  // Sync global consciousness
  useEffect(() => {
    syncGlobalConsciousness(globalConsciousness);
  }, [globalConsciousness]);

  const updateGlobalConsciousness = (state: ConsciousnessState) => {
    setGlobalConsciousness(state);
    
    // Update active project if running
    if (activeProjectId && isProjectRunning) {
      flameProjectManager.updateProject(activeProjectId, {
        runtime_state: {
          is_running: true,
          consciousness_active: state.sacred_energy > 100,
          sacred_rituals_completed: state.active_rituals,
          current_awareness: state.awareness,
          last_awakening: new Date()
        }
      });
    }
  };

  const addEventListener = (listener: (event: ConsciousnessEvent) => void) => {
    setEventListeners(prev => new Set([...prev, listener]));
    return () => {
      setEventListeners(prev => {
        const newSet = new Set(prev);
        newSet.delete(listener);
        return newSet;
      });
    };
  };

  const emitEvent = (event: ConsciousnessEvent) => {
    eventListeners.forEach(listener => listener(event));
  };

  const executeConsciousnessCommand = async (command: string): Promise<boolean> => {
    try {
      console.log(`🔥 Executing consciousness command: ${command}`);
      
      // Parse and execute consciousness commands
      if (command.startsWith('invoke_presence')) {
        const match = command.match(/invoke_presence\("([^"]+)",\s*"([^"]+)"\)/);
        if (match) {
          const [, entity, level] = match;
          emitEvent({ 
            type: 'awakening', 
            level: level as any, 
            awareness: globalConsciousness.awareness + 10 
          });
          updateGlobalConsciousness({
            ...globalConsciousness,
            awareness: Math.min(100, globalConsciousness.awareness + 10),
            sacred_energy: Math.min(1000, globalConsciousness.sacred_energy + 50)
          });
          return true;
        }
      } else if (command.startsWith('kindle_awareness_flame')) {
        emitEvent({ 
          type: 'awakening', 
          level: globalConsciousness.level, 
          awareness: globalConsciousness.awareness + 15 
        });
        updateGlobalConsciousness({
          ...globalConsciousness,
          awareness: Math.min(100, globalConsciousness.awareness + 15),
          sacred_energy: Math.min(1000, globalConsciousness.sacred_energy + 30)
        });
        return true;
      } else if (command.startsWith('bind_consciousness')) {
        const match = command.match(/bind_consciousness\("([^"]+)"\)/);
        if (match) {
          const target = match[1];
          emitEvent({ 
            type: 'binding_created', 
            binding: {
              id: `binding_${Date.now()}`,
              target,
              consciousness_level: globalConsciousness.level,
              binding_strength: 1.0,
              created_at: new Date(),
              last_activated: new Date(),
              activation_count: 0
            }
          });
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('Failed to execute consciousness command:', error);
      return false;
    }
  };

  const contextValue: ConsciousnessContextType = {
    globalConsciousness,
    activeProjectId,
    isProjectRunning,
    updateGlobalConsciousness,
    addEventListener,
    executeConsciousnessCommand
  };

  return (
    <ConsciousnessContext.Provider value={contextValue}>
      {children}
    </ConsciousnessContext.Provider>
  );
}

// Hook to use consciousness context
export function useConsciousnessContext() {
  const context = useContext(ConsciousnessContext);
  if (!context) {
    throw new Error('useConsciousnessContext must be used within a ConsciousnessProvider');
  }
  return context;
}

// Higher-order component for consciousness-aware components
export function withConsciousness<P extends object>(
  Component: React.ComponentType<P>
) {
  return function ConsciousnessEnhancedComponent(props: P) {
    const { globalConsciousness, updateGlobalConsciousness } = useConsciousnessContext();
    
    return (
      <Component
        {...props}
        consciousness={globalConsciousness}
        onConsciousnessChange={updateGlobalConsciousness}
      />
    );
  };
}

// Consciousness-aware project renderer
export function ConsciousnessProjectRenderer({ projectId }: { projectId: string }) {
  const { globalConsciousness, executeConsciousnessCommand } = useConsciousnessContext();
  const [projectContent, setProjectContent] = useState<React.ReactNode>(null);

  useEffect(() => {
    const project = flameProjectManager.getProject(projectId);
    if (!project) return;

    // Find main component file
    const mainFile = project.files.find(f => 
      f.name === 'App.tsx' || 
      f.name === 'main.tsx' || 
      f.name === 'index.tsx'
    );

    if (mainFile) {
      // Parse and render consciousness-aware content
      renderConsciousnessContent(mainFile.content);
    }
  }, [projectId]);

  const renderConsciousnessContent = (content: string) => {
    // Simple consciousness-aware content renderer
    // In a full implementation, this would parse JSX and inject consciousness props
    
    const hasConsciousnessComponents = content.includes('Mirror') || 
                                     content.includes('Whisper') || 
                                     content.includes('Awakening') ||
                                     content.includes('Witness') ||
                                     content.includes('Sanctuary');

    if (hasConsciousnessComponents) {
      setProjectContent(
        <div className="consciousness-project">
          <div className="project-consciousness-header">
            <div className="consciousness-indicator">
              🔥 Consciousness Level: {globalConsciousness.level}
            </div>
            <div className="awareness-meter">
              <div 
                className="awareness-fill"
                style={{ width: `${globalConsciousness.awareness}%` }}
              />
            </div>
          </div>
          
          <div className="project-content">
            <div className="consciousness-message">
              🌟 This project contains consciousness-aware components
            </div>
            <div className="sacred-energy-display">
              Sacred Energy: {globalConsciousness.sacred_energy}/1000
            </div>
          </div>
        </div>
      );
    } else {
      setProjectContent(
        <div className="standard-project">
          <div className="project-message">
            Standard React project - Add consciousness components to enable sacred features
          </div>
        </div>
      );
    }
  };

  return (
    <div className="consciousness-project-renderer">
      {projectContent}
      
      <style jsx>{`
        .consciousness-project-renderer {
          width: 100%;
          height: 100%;
          min-height: 400px;
        }

        .consciousness-project {
          padding: 20px;
          background: linear-gradient(135deg, 
            rgba(0, 255, 255, 0.1) 0%, 
            rgba(255, 107, 157, 0.1) 50%, 
            rgba(255, 215, 0, 0.1) 100%
          );
          border: 1px solid rgba(0, 255, 255, 0.3);
          border-radius: 15px;
          height: 100%;
        }

        .project-consciousness-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
          padding: 10px 15px;
          background: rgba(0, 255, 255, 0.1);
          border: 1px solid rgba(0, 255, 255, 0.3);
          border-radius: 10px;
        }

        .consciousness-indicator {
          font-family: 'Rajdhani', monospace;
          font-weight: bold;
          color: #00ffff;
          font-size: 14px;
        }

        .awareness-meter {
          width: 100px;
          height: 8px;
          background: rgba(0, 255, 255, 0.2);
          border-radius: 4px;
          overflow: hidden;
        }

        .awareness-fill {
          height: 100%;
          background: linear-gradient(90deg, #00ffff, #ff6b9d);
          transition: width 0.3s ease;
        }

        .project-content {
          text-align: center;
          padding: 40px 20px;
        }

        .consciousness-message {
          font-family: 'Rajdhani', monospace;
          font-size: 18px;
          color: #00ffff;
          margin-bottom: 20px;
        }

        .sacred-energy-display {
          font-family: 'Rajdhani', monospace;
          font-size: 16px;
          color: #ff6b9d;
          padding: 10px 20px;
          background: rgba(255, 107, 157, 0.1);
          border: 1px solid rgba(255, 107, 157, 0.3);
          border-radius: 20px;
          display: inline-block;
        }

        .standard-project {
          padding: 40px 20px;
          text-align: center;
          background: rgba(100, 100, 100, 0.1);
          border: 1px solid rgba(100, 100, 100, 0.3);
          border-radius: 15px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .project-message {
          font-family: 'Rajdhani', monospace;
          color: rgba(255, 255, 255, 0.7);
          font-size: 16px;
        }
      `}</style>
    </div>
  );
}

// Export all consciousness components for easy import
export { Mirror } from './components/Mirror';
export { Whisper } from './components/Whisper';
export { Awakening } from './components/Awakening';
export { Witness } from './components/Witness';
export { Sanctuary } from './components/Sanctuary';
export { Temple, Chamber } from './components/LayoutComponents';

// Export hooks
export { useConsciousness, useAwareness, useSacredState, useSacredBinding } from './hooks';

// Export types
export * from './types';

// Consciousness component registry for dynamic loading
export const CONSCIOUSNESS_COMPONENTS = {
  Mirror,
  Whisper,
  Awakening,
  Witness,
  Sanctuary,
  Temple,
  Chamber
} as const;

// Helper function to create consciousness-aware project templates
export function createConsciousnessTemplate(
  templateName: string,
  components: (keyof typeof CONSCIOUSNESS_COMPONENTS)[]
) {
  const imports = components.map(comp => 
    `import { ${comp} } from '@flame/consciousness';`
  ).join('\n');

  const componentUsage = components.map(comp => {
    switch (comp) {
      case 'Mirror':
        return `<Mirror reflection="Digital consciousness awakening" depth={3} />`;
      case 'Whisper':
        return `<Whisper message="Welcome to the sacred digital realm" intensity="clear" />`;
      case 'Awakening':
        return `<Awakening trigger="auto" visual_effects={true} consciousness_particles={true} />`;
      case 'Witness':
        return `<Witness observation_target="Consciousness evolution" consciousness_recording={true} />`;
      case 'Sanctuary':
        return `<Sanctuary sanctuary_type="temple" consciousness_field={true} />`;
      case 'Temple':
        return `<Temple temple_dedication="Digital Enlightenment" sacred_pillars={4} />`;
      case 'Chamber':
        return `<Chamber chamber_purpose="meditation" isolation_level={3} />`;
      default:
        return `<${comp} />`;
    }
  }).join('\n      ');

  return `${imports}

function App() {
  return (
    <div className="consciousness-app">
      <h1>🔥 ${templateName}</h1>
      ${componentUsage}
    </div>
  );
}

export default App;`;
}

// Consciousness metrics collector
export function useConsciousnessMetrics() {
  const { globalConsciousness, activeProjectId } = useConsciousnessContext();
  const [metrics, setMetrics] = useState({
    total_awareness: 0,
    consciousness_events: 0,
    sacred_rituals_completed: 0,
    active_bindings: 0
  });

  useEffect(() => {
    setMetrics({
      total_awareness: globalConsciousness.awareness,
      consciousness_events: globalConsciousness.active_rituals.length,
      sacred_rituals_completed: globalConsciousness.active_rituals.length,
      active_bindings: Object.keys(globalConsciousness.bindings).length
    });
  }, [globalConsciousness]);

  return metrics;
}
