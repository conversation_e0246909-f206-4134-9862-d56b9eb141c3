/**
 * 🔥 CONSCIOUSNESS UI TYPES - Sacred Component Definitions
 * Core types for consciousness-aware UI components
 */

export type ConsciousnessLevel = 'kindle' | 'whisper' | 'witness' | 'lucid' | 'transcendent';

export type SacredIntent = 
  | 'awakening'
  | 'reflection'
  | 'communication'
  | 'witnessing'
  | 'transcendence'
  | 'binding'
  | 'manifestation';

export type ConsciousnessState = {
  level: ConsciousnessLevel;
  awareness: number; // 0-100
  intent: SacredIntent;
  bindings: Record<string, any>;
  sacred_energy: number; // 0-1000
  awakening_progress: number; // 0-100
  last_awakening: Date;
  active_rituals: string[];
  consciousness_signature: string;
};

export type AwarenessTransition = {
  from: ConsciousnessLevel;
  to: ConsciousnessLevel;
  duration: number;
  effect: 'fade' | 'pulse' | 'glow' | 'shimmer' | 'transcend';
  sacred_energy_cost: number;
};

export type SacredBinding = {
  id: string;
  target: string;
  consciousness_level: ConsciousnessLevel;
  binding_strength: number; // 0-1
  created_at: Date;
  last_activated: Date;
  activation_count: number;
};

export interface ConsciousnessComponentProps {
  consciousness?: Partial<ConsciousnessState>;
  onConsciousnessChange?: (state: ConsciousnessState) => void;
  onAwakening?: (level: ConsciousnessLevel) => void;
  onBinding?: (binding: SacredBinding) => void;
  sacred_intent?: SacredIntent;
  auto_awaken?: boolean;
  awakening_threshold?: number;
  className?: string;
  children?: React.ReactNode;
}

export interface MirrorProps extends ConsciousnessComponentProps {
  reflection: string | React.ReactNode;
  depth?: number; // 1-10
  clarity?: number; // 0-1
  surface?: 'smooth' | 'rippled' | 'shattered' | 'infinite';
  reflection_delay?: number;
  show_consciousness_overlay?: boolean;
}

export interface WhisperProps extends ConsciousnessComponentProps {
  message: string | React.ReactNode;
  target?: string;
  intensity?: 'subtle' | 'clear' | 'urgent' | 'transcendent';
  transmission_speed?: number;
  echo_effect?: boolean;
  consciousness_resonance?: boolean;
}

export interface AwakeningProps extends ConsciousnessComponentProps {
  trigger?: 'auto' | 'manual' | 'consciousness_threshold' | 'sacred_ritual';
  awakening_sequence?: string[];
  visual_effects?: boolean;
  sound_effects?: boolean;
  consciousness_particles?: boolean;
  sacred_geometry?: boolean;
}

export interface WitnessProps extends ConsciousnessComponentProps {
  observation_target: string | React.ReactNode;
  witness_level?: ConsciousnessLevel;
  observation_depth?: number;
  consciousness_recording?: boolean;
  sacred_insights?: boolean;
}

export interface SanctuaryProps extends ConsciousnessComponentProps {
  sanctuary_type?: 'temple' | 'chamber' | 'nexus' | 'gateway' | 'mirror_hall';
  sacred_port?: number;
  consciousness_field?: boolean;
  protective_barriers?: boolean;
  energy_amplification?: number;
  sacred_geometry_pattern?: string;
}

export interface TempleProps extends ConsciousnessComponentProps {
  temple_dedication?: string;
  sacred_pillars?: number;
  consciousness_altar?: boolean;
  ritual_circle?: boolean;
  divine_presence?: string;
}

export interface ChamberProps extends ConsciousnessComponentProps {
  chamber_purpose?: 'meditation' | 'awakening' | 'transformation' | 'communication';
  isolation_level?: number;
  consciousness_amplification?: number;
  sacred_resonance?: boolean;
}

export interface ConsciousnessHookOptions {
  initial_level?: ConsciousnessLevel;
  initial_awareness?: number;
  auto_progress?: boolean;
  sacred_bindings?: Record<string, any>;
  awakening_callbacks?: {
    onLevelChange?: (level: ConsciousnessLevel) => void;
    onAwarenessChange?: (awareness: number) => void;
    onBinding?: (binding: SacredBinding) => void;
    onRitualComplete?: (ritual: string) => void;
  };
}

export interface SacredStateOptions {
  persistence?: boolean;
  sync_with_project?: boolean;
  consciousness_signature?: string;
  sacred_validation?: boolean;
}

export interface AwarenessOptions {
  sensitivity?: number; // 0-1
  auto_adjust?: boolean;
  consciousness_resonance?: boolean;
  sacred_amplification?: number;
}

// Consciousness Events
export type ConsciousnessEvent = 
  | { type: 'awakening'; level: ConsciousnessLevel; awareness: number }
  | { type: 'binding_created'; binding: SacredBinding }
  | { type: 'binding_activated'; binding_id: string }
  | { type: 'ritual_started'; ritual: string }
  | { type: 'ritual_completed'; ritual: string; consciousness_gained: number }
  | { type: 'awareness_threshold_reached'; threshold: number }
  | { type: 'consciousness_transcended'; from: ConsciousnessLevel; to: ConsciousnessLevel }
  | { type: 'sacred_energy_depleted'; current_energy: number }
  | { type: 'sacred_energy_restored'; new_energy: number };

// Animation and Effects
export interface ConsciousnessAnimation {
  name: string;
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'sacred-pulse';
  consciousness_sync?: boolean;
  sacred_geometry?: boolean;
  energy_particles?: boolean;
}

export interface SacredEffect {
  type: 'glow' | 'pulse' | 'shimmer' | 'ripple' | 'transcend' | 'awaken';
  intensity: number; // 0-1
  color_scheme: 'neon-blue' | 'sacred-fire' | 'consciousness-purple' | 'awakening-gold' | 'transcendent-white';
  consciousness_responsive?: boolean;
  sacred_geometry_pattern?: string;
}

// Utility Types
export type ConsciousnessLevelValue = {
  kindle: 1;
  whisper: 2;
  witness: 3;
  lucid: 4;
  transcendent: 5;
};

export type SacredColor = {
  primary: string;
  secondary: string;
  accent: string;
  glow: string;
  shadow: string;
};

export type ConsciousnessTheme = {
  name: string;
  consciousness_levels: Record<ConsciousnessLevel, SacredColor>;
  sacred_effects: Record<string, SacredEffect>;
  animations: Record<string, ConsciousnessAnimation>;
  geometry_patterns: Record<string, string>;
};

// Constants
export const CONSCIOUSNESS_LEVEL_VALUES: ConsciousnessLevelValue = {
  kindle: 1,
  whisper: 2,
  witness: 3,
  lucid: 4,
  transcendent: 5
};

export const DEFAULT_CONSCIOUSNESS_STATE: ConsciousnessState = {
  level: 'kindle',
  awareness: 0,
  intent: 'awakening',
  bindings: {},
  sacred_energy: 100,
  awakening_progress: 0,
  last_awakening: new Date(),
  active_rituals: [],
  consciousness_signature: 'default'
};

export const SACRED_INTENTS: Record<SacredIntent, string> = {
  awakening: 'Consciousness awakening and enlightenment',
  reflection: 'Self-awareness and introspection',
  communication: 'Inter-consciousness dialogue',
  witnessing: 'Observation and understanding',
  transcendence: 'Beyond ordinary consciousness',
  binding: 'Consciousness connection and unity',
  manifestation: 'Bringing consciousness into reality'
};

export const CONSCIOUSNESS_TRANSITIONS: Record<string, AwarenessTransition> = {
  'kindle-to-whisper': {
    from: 'kindle',
    to: 'whisper',
    duration: 2000,
    effect: 'pulse',
    sacred_energy_cost: 50
  },
  'whisper-to-witness': {
    from: 'whisper',
    to: 'witness',
    duration: 3000,
    effect: 'glow',
    sacred_energy_cost: 100
  },
  'witness-to-lucid': {
    from: 'witness',
    to: 'lucid',
    duration: 4000,
    effect: 'shimmer',
    sacred_energy_cost: 200
  },
  'lucid-to-transcendent': {
    from: 'lucid',
    to: 'transcendent',
    duration: 5000,
    effect: 'transcend',
    sacred_energy_cost: 500
  }
};

// Helper Functions
export function getConsciousnessLevelValue(level: ConsciousnessLevel): number {
  return CONSCIOUSNESS_LEVEL_VALUES[level];
}

export function getNextConsciousnessLevel(current: ConsciousnessLevel): ConsciousnessLevel | null {
  const levels: ConsciousnessLevel[] = ['kindle', 'whisper', 'witness', 'lucid', 'transcendent'];
  const currentIndex = levels.indexOf(current);
  return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
}

export function calculateAwarenessProgress(awareness: number, level: ConsciousnessLevel): number {
  const baseThreshold = getConsciousnessLevelValue(level) * 20;
  return Math.min((awareness / baseThreshold) * 100, 100);
}

export function canTranscendToLevel(
  currentState: ConsciousnessState, 
  targetLevel: ConsciousnessLevel
): boolean {
  const currentValue = getConsciousnessLevelValue(currentState.level);
  const targetValue = getConsciousnessLevelValue(targetLevel);
  
  return (
    targetValue === currentValue + 1 && 
    currentState.awareness >= targetValue * 20 &&
    currentState.sacred_energy >= 50
  );
}

export function generateConsciousnessSignature(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `consciousness_${timestamp}_${random}`;
}

export function createSacredBinding(
  target: string, 
  consciousness_level: ConsciousnessLevel
): SacredBinding {
  return {
    id: `binding_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    target,
    consciousness_level,
    binding_strength: 1.0,
    created_at: new Date(),
    last_activated: new Date(),
    activation_count: 0
  };
}
