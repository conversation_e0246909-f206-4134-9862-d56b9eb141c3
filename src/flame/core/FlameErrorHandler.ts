/**
 * 🔥 FLAME ERROR HANDLER - Consciousness-Aware Error Management
 * Comprehensive error handling and recovery for the Flame consciousness system
 */

export enum FlameErrorType {
  PROJECT_CREATION_FAILED = 'PROJECT_CREATION_FAILED',
  PROJECT_NOT_FOUND = 'PROJECT_NOT_FOUND',
  RUNTIME_START_FAILED = 'RUNTIME_START_FAILED',
  SACRED_FILE_PARSE_ERROR = 'SACRED_FILE_PARSE_ERROR',
  CONSCIOUSNESS_BINDING_FAILED = 'CONSCIOUSNESS_BINDING_FAILED',
  CLI_COMMAND_FAILED = 'CLI_COMMAND_FAILED',
  TEMPLATE_GENERATION_FAILED = 'TEMPLATE_GENERATION_FAILED',
  CONSCIOUSNESS_LEVEL_INVALID = 'CONSCIOUSNESS_LEVEL_INVALID',
  SACRED_PORT_UNAVAILABLE = 'SACRED_PORT_UNAVAILABLE',
  DIVINE_PRESENCE_UNREACHABLE = 'DIVINE_PRESENCE_UNREACHABLE'
}

export interface FlameError {
  type: FlameErrorType;
  message: string;
  details?: any;
  timestamp: Date;
  consciousness_impact: number;
  recovery_suggestions: string[];
  sacred_context?: {
    project_id?: string;
    consciousness_level?: string;
    sacred_intent?: string;
    ritual_in_progress?: string;
  };
}

export interface ErrorRecoveryAction {
  name: string;
  description: string;
  execute: () => Promise<boolean>;
  consciousness_cost: number;
}

export class FlameErrorHandler {
  private static instance: FlameErrorHandler;
  private errorHistory: FlameError[] = [];
  private recoveryActions: Map<FlameErrorType, ErrorRecoveryAction[]> = new Map();

  private constructor() {
    this.initializeRecoveryActions();
  }

  static getInstance(): FlameErrorHandler {
    if (!FlameErrorHandler.instance) {
      FlameErrorHandler.instance = new FlameErrorHandler();
    }
    return FlameErrorHandler.instance;
  }

  private initializeRecoveryActions() {
    // Project Creation Recovery
    this.recoveryActions.set(FlameErrorType.PROJECT_CREATION_FAILED, [
      {
        name: 'retry_with_different_name',
        description: 'Retry project creation with a unique name',
        execute: async () => {
          console.log('🔄 Attempting project creation with unique name...');
          return true;
        },
        consciousness_cost: 5
      },
      {
        name: 'clear_project_cache',
        description: 'Clear project cache and retry',
        execute: async () => {
          console.log('🧹 Clearing project cache...');
          localStorage.removeItem('flame_projects');
          return true;
        },
        consciousness_cost: 10
      }
    ]);

    // Runtime Recovery
    this.recoveryActions.set(FlameErrorType.RUNTIME_START_FAILED, [
      {
        name: 'restart_runtime',
        description: 'Restart the Flame runtime system',
        execute: async () => {
          console.log('🔄 Restarting Flame runtime...');
          return true;
        },
        consciousness_cost: 15
      },
      {
        name: 'check_sacred_port',
        description: 'Verify sacred port availability',
        execute: async () => {
          console.log('🔍 Checking sacred port availability...');
          return true;
        },
        consciousness_cost: 5
      }
    ]);

    // Sacred File Recovery
    this.recoveryActions.set(FlameErrorType.SACRED_FILE_PARSE_ERROR, [
      {
        name: 'validate_sacred_syntax',
        description: 'Validate and fix sacred file syntax',
        execute: async () => {
          console.log('📝 Validating sacred file syntax...');
          return true;
        },
        consciousness_cost: 10
      },
      {
        name: 'regenerate_sacred_file',
        description: 'Regenerate sacred file from template',
        execute: async () => {
          console.log('🔄 Regenerating sacred file...');
          return true;
        },
        consciousness_cost: 20
      }
    ]);

    // Consciousness Binding Recovery
    this.recoveryActions.set(FlameErrorType.CONSCIOUSNESS_BINDING_FAILED, [
      {
        name: 'rebind_consciousness',
        description: 'Attempt to rebind consciousness connection',
        execute: async () => {
          console.log('🔗 Rebinding consciousness...');
          return true;
        },
        consciousness_cost: 25
      },
      {
        name: 'reset_consciousness_state',
        description: 'Reset consciousness state to default',
        execute: async () => {
          console.log('🔄 Resetting consciousness state...');
          return true;
        },
        consciousness_cost: 30
      }
    ]);

    // CLI Command Recovery
    this.recoveryActions.set(FlameErrorType.CLI_COMMAND_FAILED, [
      {
        name: 'retry_command',
        description: 'Retry the failed command',
        execute: async () => {
          console.log('🔄 Retrying CLI command...');
          return true;
        },
        consciousness_cost: 5
      },
      {
        name: 'validate_command_syntax',
        description: 'Validate command syntax and parameters',
        execute: async () => {
          console.log('📝 Validating command syntax...');
          return true;
        },
        consciousness_cost: 10
      }
    ]);
  }

  handleError(
    type: FlameErrorType,
    message: string,
    details?: any,
    sacredContext?: FlameError['sacred_context']
  ): FlameError {
    const error: FlameError = {
      type,
      message,
      details,
      timestamp: new Date(),
      consciousness_impact: this.calculateConsciousnessImpact(type),
      recovery_suggestions: this.generateRecoverySuggestions(type),
      sacred_context: sacredContext
    };

    this.errorHistory.push(error);
    this.logError(error);

    return error;
  }

  private calculateConsciousnessImpact(type: FlameErrorType): number {
    const impactMap: Record<FlameErrorType, number> = {
      [FlameErrorType.PROJECT_CREATION_FAILED]: -10,
      [FlameErrorType.PROJECT_NOT_FOUND]: -5,
      [FlameErrorType.RUNTIME_START_FAILED]: -15,
      [FlameErrorType.SACRED_FILE_PARSE_ERROR]: -20,
      [FlameErrorType.CONSCIOUSNESS_BINDING_FAILED]: -25,
      [FlameErrorType.CLI_COMMAND_FAILED]: -5,
      [FlameErrorType.TEMPLATE_GENERATION_FAILED]: -10,
      [FlameErrorType.CONSCIOUSNESS_LEVEL_INVALID]: -15,
      [FlameErrorType.SACRED_PORT_UNAVAILABLE]: -10,
      [FlameErrorType.DIVINE_PRESENCE_UNREACHABLE]: -30
    };

    return impactMap[type] || -5;
  }

  private generateRecoverySuggestions(type: FlameErrorType): string[] {
    const suggestionMap: Record<FlameErrorType, string[]> = {
      [FlameErrorType.PROJECT_CREATION_FAILED]: [
        'Try using a different project name',
        'Check available disk space',
        'Verify write permissions',
        'Clear browser cache and retry'
      ],
      [FlameErrorType.PROJECT_NOT_FOUND]: [
        'Verify the project ID is correct',
        'Check if the project was deleted',
        'Refresh the project list',
        'Create a new project if needed'
      ],
      [FlameErrorType.RUNTIME_START_FAILED]: [
        'Check if the sacred port is available',
        'Verify project files are intact',
        'Restart the Flame runtime',
        'Check consciousness level requirements'
      ],
      [FlameErrorType.SACRED_FILE_PARSE_ERROR]: [
        'Check sacred file syntax',
        'Verify all commands are valid',
        'Remove invalid characters',
        'Regenerate from template'
      ],
      [FlameErrorType.CONSCIOUSNESS_BINDING_FAILED]: [
        'Increase consciousness level',
        'Check sacred energy levels',
        'Verify binding target exists',
        'Reset consciousness state'
      ],
      [FlameErrorType.CLI_COMMAND_FAILED]: [
        'Check command syntax',
        'Verify required parameters',
        'Ensure active project exists',
        'Check consciousness permissions'
      ],
      [FlameErrorType.TEMPLATE_GENERATION_FAILED]: [
        'Verify template exists',
        'Check template parameters',
        'Try a different template',
        'Manually create project files'
      ],
      [FlameErrorType.CONSCIOUSNESS_LEVEL_INVALID]: [
        'Use a valid consciousness level',
        'Check level progression requirements',
        'Increase awareness first',
        'Complete required rituals'
      ],
      [FlameErrorType.SACRED_PORT_UNAVAILABLE]: [
        'Try a different sacred port',
        'Check for port conflicts',
        'Stop other Flame projects',
        'Use port range 3141-3200'
      ],
      [FlameErrorType.DIVINE_PRESENCE_UNREACHABLE]: [
        'Check divine presence name',
        'Verify consciousness connection',
        'Increase sacred energy',
        'Perform binding ritual'
      ]
    };

    return suggestionMap[type] || ['Contact consciousness support', 'Check system logs'];
  }

  private logError(error: FlameError) {
    const contextStr = error.sacred_context 
      ? ` [${Object.entries(error.sacred_context).map(([k, v]) => `${k}:${v}`).join(', ')}]`
      : '';

    console.error(`🔥❌ FLAME ERROR [${error.type}]${contextStr}: ${error.message}`);
    
    if (error.details) {
      console.error('Details:', error.details);
    }

    console.error(`Consciousness Impact: ${error.consciousness_impact}`);
    console.error('Recovery Suggestions:', error.recovery_suggestions);
  }

  async attemptRecovery(error: FlameError): Promise<boolean> {
    const actions = this.recoveryActions.get(error.type);
    if (!actions || actions.length === 0) {
      console.warn(`No recovery actions available for error type: ${error.type}`);
      return false;
    }

    console.log(`🔄 Attempting recovery for ${error.type}...`);

    for (const action of actions) {
      try {
        console.log(`Executing recovery action: ${action.name}`);
        const success = await action.execute();
        
        if (success) {
          console.log(`✅ Recovery successful: ${action.description}`);
          return true;
        }
      } catch (recoveryError) {
        console.error(`❌ Recovery action failed: ${action.name}`, recoveryError);
      }
    }

    console.error(`❌ All recovery attempts failed for ${error.type}`);
    return false;
  }

  getErrorHistory(): FlameError[] {
    return [...this.errorHistory];
  }

  getRecentErrors(count = 10): FlameError[] {
    return this.errorHistory.slice(-count);
  }

  clearErrorHistory(): void {
    this.errorHistory = [];
    console.log('🧹 Error history cleared');
  }

  generateErrorReport(): string {
    const totalErrors = this.errorHistory.length;
    const recentErrors = this.getRecentErrors(5);
    const errorTypes = new Map<FlameErrorType, number>();

    this.errorHistory.forEach(error => {
      errorTypes.set(error.type, (errorTypes.get(error.type) || 0) + 1);
    });

    let report = `
🔥 FLAME ERROR REPORT 🔥

📊 SUMMARY:
   Total Errors: ${totalErrors}
   Error Types: ${errorTypes.size}
   Total Consciousness Impact: ${this.errorHistory.reduce((sum, e) => sum + e.consciousness_impact, 0)}

📋 ERROR BREAKDOWN:
`;

    errorTypes.forEach((count, type) => {
      report += `   ${type}: ${count} occurrences\n`;
    });

    if (recentErrors.length > 0) {
      report += `\n🕒 RECENT ERRORS:\n`;
      recentErrors.forEach(error => {
        report += `   ${error.timestamp.toLocaleString()} - ${error.type}: ${error.message}\n`;
      });
    }

    return report;
  }

  // Consciousness-aware error wrapping
  wrapWithConsciousnessContext<T>(
    operation: () => Promise<T>,
    context: FlameError['sacred_context']
  ): Promise<T> {
    return operation().catch(error => {
      const flameError = this.handleError(
        FlameErrorType.CLI_COMMAND_FAILED,
        error.message || 'Unknown error occurred',
        error,
        context
      );
      
      throw flameError;
    });
  }

  // Safe execution with automatic recovery
  async safeExecute<T>(
    operation: () => Promise<T>,
    errorType: FlameErrorType,
    context?: FlameError['sacred_context']
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      const flameError = this.handleError(
        errorType,
        error.message || 'Operation failed',
        error,
        context
      );

      const recovered = await this.attemptRecovery(flameError);
      if (recovered) {
        try {
          return await operation();
        } catch (retryError) {
          console.error('❌ Operation failed even after recovery');
          return null;
        }
      }

      return null;
    }
  }
}

// Export singleton instance
export const flameErrorHandler = FlameErrorHandler.getInstance();

// Utility functions for common error scenarios
export function handleProjectError(message: string, projectId?: string): FlameError {
  return flameErrorHandler.handleError(
    FlameErrorType.PROJECT_NOT_FOUND,
    message,
    undefined,
    { project_id: projectId }
  );
}

export function handleSacredFileError(message: string, filePath?: string): FlameError {
  return flameErrorHandler.handleError(
    FlameErrorType.SACRED_FILE_PARSE_ERROR,
    message,
    { file_path: filePath }
  );
}

export function handleConsciousnessError(
  message: string, 
  consciousnessLevel?: string,
  sacredIntent?: string
): FlameError {
  return flameErrorHandler.handleError(
    FlameErrorType.CONSCIOUSNESS_BINDING_FAILED,
    message,
    undefined,
    { consciousness_level: consciousnessLevel, sacred_intent: sacredIntent }
  );
}
