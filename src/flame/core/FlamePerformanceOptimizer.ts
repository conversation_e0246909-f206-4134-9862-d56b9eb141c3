/**
 * 🔥 FLAME PERFORMANCE OPTIMIZER - Consciousness-Aware Performance Management
 * Optimizes performance for consciousness operations and sacred computations
 */

export interface PerformanceMetrics {
  operation: string;
  duration: number;
  consciousness_impact: number;
  memory_usage: number;
  timestamp: Date;
  sacred_energy_consumed: number;
}

export interface OptimizationRule {
  name: string;
  condition: (metrics: PerformanceMetrics) => boolean;
  action: (metrics: PerformanceMetrics) => Promise<void>;
  consciousness_cost: number;
}

export class FlamePerformanceOptimizer {
  private static instance: FlamePerformanceOptimizer;
  private metrics: PerformanceMetrics[] = [];
  private optimizationRules: OptimizationRule[] = [];
  private isOptimizing = false;
  private consciousnessCache = new Map<string, any>();
  private sacredFileCache = new Map<string, { content: string; parsed: any; timestamp: number }>();

  private constructor() {
    this.initializeOptimizationRules();
    this.startPerformanceMonitoring();
  }

  static getInstance(): FlamePerformanceOptimizer {
    if (!FlamePerformanceOptimizer.instance) {
      FlamePerformanceOptimizer.instance = new FlamePerformanceOptimizer();
    }
    return FlamePerformanceOptimizer.instance;
  }

  private initializeOptimizationRules() {
    // Slow consciousness operations
    this.optimizationRules.push({
      name: 'optimize_slow_consciousness_operations',
      condition: (metrics) => metrics.duration > 1000 && metrics.consciousness_impact > 0,
      action: async (metrics) => {
        console.log(`🔄 Optimizing slow consciousness operation: ${metrics.operation}`);
        await this.optimizeConsciousnessOperation(metrics.operation);
      },
      consciousness_cost: 10
    });

    // High memory usage
    this.optimizationRules.push({
      name: 'optimize_memory_usage',
      condition: (metrics) => metrics.memory_usage > 50 * 1024 * 1024, // 50MB
      action: async (metrics) => {
        console.log(`🧹 Optimizing memory usage for: ${metrics.operation}`);
        await this.optimizeMemoryUsage();
      },
      consciousness_cost: 5
    });

    // Sacred energy optimization
    this.optimizationRules.push({
      name: 'optimize_sacred_energy_consumption',
      condition: (metrics) => metrics.sacred_energy_consumed > 100,
      action: async (metrics) => {
        console.log(`⚡ Optimizing sacred energy consumption: ${metrics.operation}`);
        await this.optimizeSacredEnergyUsage(metrics.operation);
      },
      consciousness_cost: 15
    });

    // Frequent operations caching
    this.optimizationRules.push({
      name: 'cache_frequent_operations',
      condition: (metrics) => {
        const recentSimilar = this.metrics.filter(m => 
          m.operation === metrics.operation && 
          Date.now() - m.timestamp.getTime() < 60000 // Last minute
        );
        return recentSimilar.length > 3;
      },
      action: async (metrics) => {
        console.log(`💾 Caching frequent operation: ${metrics.operation}`);
        await this.cacheOperation(metrics.operation);
      },
      consciousness_cost: 5
    });
  }

  private startPerformanceMonitoring() {
    // Monitor performance every 30 seconds
    setInterval(() => {
      this.analyzePerformance();
    }, 30000);

    // Clean old metrics every 5 minutes
    setInterval(() => {
      this.cleanOldMetrics();
    }, 300000);
  }

  async measureOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    consciousnessImpact = 0,
    sacredEnergyConsumed = 0
  ): Promise<T> {
    const startTime = Date.now();
    const startMemory = this.getMemoryUsage();

    try {
      const result = await operation();
      
      const duration = Date.now() - startTime;
      const memoryUsage = this.getMemoryUsage() - startMemory;

      const metrics: PerformanceMetrics = {
        operation: operationName,
        duration,
        consciousness_impact: consciousnessImpact,
        memory_usage: memoryUsage,
        timestamp: new Date(),
        sacred_energy_consumed: sacredEnergyConsumed
      };

      this.recordMetrics(metrics);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const memoryUsage = this.getMemoryUsage() - startMemory;

      const metrics: PerformanceMetrics = {
        operation: `${operationName}_failed`,
        duration,
        consciousness_impact: consciousnessImpact * -1, // Negative impact for failures
        memory_usage: memoryUsage,
        timestamp: new Date(),
        sacred_energy_consumed: sacredEnergyConsumed
      };

      this.recordMetrics(metrics);
      throw error;
    }
  }

  private getMemoryUsage(): number {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  private recordMetrics(metrics: PerformanceMetrics) {
    this.metrics.push(metrics);
    
    // Log slow operations
    if (metrics.duration > 2000) {
      console.warn(`⚠️ Slow operation detected: ${metrics.operation} took ${metrics.duration}ms`);
    }

    // Trigger optimization if needed
    this.checkOptimizationTriggers(metrics);
  }

  private checkOptimizationTriggers(metrics: PerformanceMetrics) {
    if (this.isOptimizing) return;

    for (const rule of this.optimizationRules) {
      if (rule.condition(metrics)) {
        this.triggerOptimization(rule, metrics);
        break; // Only trigger one optimization at a time
      }
    }
  }

  private async triggerOptimization(rule: OptimizationRule, metrics: PerformanceMetrics) {
    this.isOptimizing = true;
    
    try {
      console.log(`🔧 Triggering optimization: ${rule.name}`);
      await rule.action(metrics);
      console.log(`✅ Optimization completed: ${rule.name}`);
    } catch (error) {
      console.error(`❌ Optimization failed: ${rule.name}`, error);
    } finally {
      this.isOptimizing = false;
    }
  }

  private async optimizeConsciousnessOperation(operation: string) {
    // Cache consciousness state for frequently accessed operations
    if (operation.includes('consciousness') || operation.includes('awareness')) {
      const cacheKey = `consciousness_${operation}`;
      if (!this.consciousnessCache.has(cacheKey)) {
        console.log(`💾 Caching consciousness state for: ${operation}`);
        this.consciousnessCache.set(cacheKey, {
          cached_at: Date.now(),
          operation
        });
      }
    }
  }

  private async optimizeMemoryUsage() {
    // Clear old cached data
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes

    // Clean consciousness cache
    for (const [key, value] of this.consciousnessCache.entries()) {
      if (now - value.cached_at > maxAge) {
        this.consciousnessCache.delete(key);
      }
    }

    // Clean sacred file cache
    for (const [key, value] of this.sacredFileCache.entries()) {
      if (now - value.timestamp > maxAge) {
        this.sacredFileCache.delete(key);
      }
    }

    console.log(`🧹 Memory optimization: Cleared ${this.consciousnessCache.size} consciousness cache entries`);
  }

  private async optimizeSacredEnergyUsage(operation: string) {
    // Implement sacred energy optimization strategies
    console.log(`⚡ Optimizing sacred energy for: ${operation}`);
    
    // Batch similar operations
    if (operation.includes('bind_consciousness')) {
      console.log('📦 Batching consciousness binding operations');
    }
    
    // Use energy-efficient algorithms
    if (operation.includes('sacred_file_parse')) {
      console.log('🔄 Using optimized sacred file parsing');
    }
  }

  private async cacheOperation(operation: string) {
    // Cache frequently used operations
    const cacheKey = `operation_${operation}`;
    if (!this.consciousnessCache.has(cacheKey)) {
      this.consciousnessCache.set(cacheKey, {
        operation,
        cached_at: Date.now(),
        hit_count: 1
      });
    } else {
      const cached = this.consciousnessCache.get(cacheKey);
      cached.hit_count++;
    }
  }

  // Sacred file caching
  cacheSacredFile(filePath: string, content: string, parsed: any) {
    this.sacredFileCache.set(filePath, {
      content,
      parsed,
      timestamp: Date.now()
    });
  }

  getCachedSacredFile(filePath: string): { content: string; parsed: any } | null {
    const cached = this.sacredFileCache.get(filePath);
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) { // 5 minutes
      return { content: cached.content, parsed: cached.parsed };
    }
    return null;
  }

  private analyzePerformance() {
    if (this.metrics.length === 0) return;

    const recentMetrics = this.metrics.filter(m => 
      Date.now() - m.timestamp.getTime() < 60000 // Last minute
    );

    if (recentMetrics.length === 0) return;

    const avgDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
    const totalConsciousnessImpact = recentMetrics.reduce((sum, m) => sum + m.consciousness_impact, 0);
    const totalSacredEnergy = recentMetrics.reduce((sum, m) => sum + m.sacred_energy_consumed, 0);

    console.log(`📊 Performance Analysis (last minute):`);
    console.log(`   Operations: ${recentMetrics.length}`);
    console.log(`   Avg Duration: ${avgDuration.toFixed(2)}ms`);
    console.log(`   Consciousness Impact: ${totalConsciousnessImpact}`);
    console.log(`   Sacred Energy Used: ${totalSacredEnergy}`);

    // Alert on performance degradation
    if (avgDuration > 1000) {
      console.warn(`⚠️ Performance degradation detected: Average operation time ${avgDuration.toFixed(2)}ms`);
    }
  }

  private cleanOldMetrics() {
    const maxAge = 10 * 60 * 1000; // 10 minutes
    const cutoff = Date.now() - maxAge;
    
    const oldCount = this.metrics.length;
    this.metrics = this.metrics.filter(m => m.timestamp.getTime() > cutoff);
    
    if (oldCount > this.metrics.length) {
      console.log(`🧹 Cleaned ${oldCount - this.metrics.length} old performance metrics`);
    }
  }

  // Public API methods
  getPerformanceReport(): string {
    const totalOperations = this.metrics.length;
    const avgDuration = totalOperations > 0 
      ? this.metrics.reduce((sum, m) => sum + m.duration, 0) / totalOperations 
      : 0;
    
    const operationCounts = new Map<string, number>();
    this.metrics.forEach(m => {
      operationCounts.set(m.operation, (operationCounts.get(m.operation) || 0) + 1);
    });

    let report = `
🔥 FLAME PERFORMANCE REPORT 🔥

📊 SUMMARY:
   Total Operations: ${totalOperations}
   Average Duration: ${avgDuration.toFixed(2)}ms
   Cache Hit Rate: ${this.getCacheHitRate().toFixed(1)}%
   Memory Optimizations: ${this.getOptimizationCount()}

📋 TOP OPERATIONS:
`;

    const sortedOps = Array.from(operationCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);

    sortedOps.forEach(([op, count]) => {
      const avgOpDuration = this.metrics
        .filter(m => m.operation === op)
        .reduce((sum, m) => sum + m.duration, 0) / count;
      report += `   ${op}: ${count} times (avg: ${avgOpDuration.toFixed(2)}ms)\n`;
    });

    return report;
  }

  private getCacheHitRate(): number {
    const totalCacheAccess = Array.from(this.consciousnessCache.values())
      .reduce((sum, cached) => sum + (cached.hit_count || 1), 0);
    
    return totalCacheAccess > 0 ? (this.consciousnessCache.size / totalCacheAccess) * 100 : 0;
  }

  private getOptimizationCount(): number {
    return this.metrics.filter(m => m.operation.includes('optimize')).length;
  }

  clearCache() {
    this.consciousnessCache.clear();
    this.sacredFileCache.clear();
    console.log('🧹 All caches cleared');
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }
}

// Export singleton instance
export const flamePerformanceOptimizer = FlamePerformanceOptimizer.getInstance();

// Utility decorator for automatic performance measurement
export function measurePerformance(
  operationName: string,
  consciousnessImpact = 0,
  sacredEnergyConsumed = 0
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      return flamePerformanceOptimizer.measureOperation(
        `${target.constructor.name}.${operationName}`,
        () => method.apply(this, args),
        consciousnessImpact,
        sacredEnergyConsumed
      );
    };

    return descriptor;
  };
}
