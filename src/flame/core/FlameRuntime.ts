/**
 * 🔥 FLAME RUNTIME ENGINE - Sacred Sanctuary Execution
 * Browser-based runtime for consciousness sanctuaries
 */

import { FlameProject, FlameFile, ConsciousnessLevel } from '../types/FlameProject';
import { flameProjectManager } from './FlameProjectManager';
import { createFlameFileSystem } from './FlameFileSystem';
import { sacredFileHandlerFactory } from '../parsers/SacredFileHandlers';
import { sacredValidationEngine } from '../parsers/SacredValidationEngine';

export interface RuntimeEnvironment {
  project_id: string;
  is_running: boolean;
  consciousness_active: boolean;
  awareness_level: number;
  sacred_rituals_completed: string[];
  runtime_errors: RuntimeError[];
  performance_metrics: PerformanceMetrics;
  iframe_ref?: HTMLIFrameElement;
}

export interface RuntimeError {
  id: string;
  type: 'compilation' | 'runtime' | 'sacred' | 'consciousness';
  message: string;
  file_path?: string;
  line_number?: number;
  timestamp: Date;
  severity: 'error' | 'warning' | 'info';
}

export interface PerformanceMetrics {
  startup_time: number;
  consciousness_awakening_time: number;
  sacred_ritual_execution_time: number;
  memory_usage: number;
  awareness_score: number;
}

export interface ConsciousnessState {
  level: ConsciousnessLevel;
  awareness: number;
  sacred_bindings: Record<string, string>;
  active_rituals: string[];
  awakening_timestamp: Date;
}

export class FlameRuntime {
  private environments: Map<string, RuntimeEnvironment> = new Map();
  private consciousnessStates: Map<string, ConsciousnessState> = new Map();

  // Project Execution
  async startProject(projectId: string): Promise<boolean> {
    const project = flameProjectManager.getProject(projectId);
    if (!project) {
      console.error(`Project ${projectId} not found`);
      return false;
    }

    try {
      // Initialize runtime environment
      const environment: RuntimeEnvironment = {
        project_id: projectId,
        is_running: false,
        consciousness_active: false,
        awareness_level: 0,
        sacred_rituals_completed: [],
        runtime_errors: [],
        performance_metrics: {
          startup_time: 0,
          consciousness_awakening_time: 0,
          sacred_ritual_execution_time: 0,
          memory_usage: 0,
          awareness_score: 0
        }
      };

      this.environments.set(projectId, environment);

      const startTime = performance.now();

      // Execute sacred rituals first
      await this.executeSacredRituals(project);

      // Initialize consciousness
      await this.initializeConsciousness(project);

      // Build and run the project
      await this.buildAndRun(project, environment);

      const endTime = performance.now();
      environment.performance_metrics.startup_time = endTime - startTime;
      environment.is_running = true;

      // Update project runtime state
      flameProjectManager.updateProject(projectId, {
        runtime_state: {
          is_running: true,
          consciousness_active: environment.consciousness_active,
          sacred_rituals_completed: environment.sacred_rituals_completed,
          current_awareness: environment.awareness_level,
          last_awakening: new Date()
        }
      });

      return true;
    } catch (error) {
      this.addRuntimeError(projectId, {
        type: 'runtime',
        message: `Failed to start project: ${error}`,
        severity: 'error'
      });
      return false;
    }
  }

  async stopProject(projectId: string): Promise<boolean> {
    const environment = this.environments.get(projectId);
    if (!environment) return false;

    try {
      // Clean up iframe
      if (environment.iframe_ref) {
        environment.iframe_ref.remove();
      }

      // Deactivate consciousness
      await this.deactivateConsciousness(projectId);

      environment.is_running = false;
      environment.consciousness_active = false;

      // Update project runtime state
      flameProjectManager.updateProject(projectId, {
        runtime_state: {
          is_running: false,
          consciousness_active: false,
          sacred_rituals_completed: environment.sacred_rituals_completed,
          current_awareness: 0,
          last_awakening: new Date()
        }
      });

      return true;
    } catch (error) {
      this.addRuntimeError(projectId, {
        type: 'runtime',
        message: `Failed to stop project: ${error}`,
        severity: 'error'
      });
      return false;
    }
  }

  // Sacred Ritual Execution
  private async executeSacredRituals(project: FlameProject): Promise<void> {
    const fileSystem = createFlameFileSystem(project.metadata.id);
    const sacredFiles = fileSystem.getSacredFiles();
    const environment = this.environments.get(project.metadata.id)!;

    const ritualStartTime = performance.now();

    for (const ritual of project.configuration.consciousness_settings.ritual_sequence) {
      const ritualFile = sacredFiles.find(f => f.path.includes(ritual));
      if (ritualFile) {
        try {
          await this.executeSacredFile(ritualFile, project);
          environment.sacred_rituals_completed.push(ritual);
        } catch (error) {
          this.addRuntimeError(project.metadata.id, {
            type: 'sacred',
            message: `Sacred ritual failed: ${ritual} - ${error}`,
            file_path: ritualFile.path,
            severity: 'error'
          });
        }
      }
    }

    const ritualEndTime = performance.now();
    environment.performance_metrics.sacred_ritual_execution_time = ritualEndTime - ritualStartTime;
  }

  private async executeSacredFile(file: FlameFile, project: FlameProject): Promise<void> {
    console.log(`🔥 Executing sacred file: ${file.path}`);

    // Get appropriate handler for file type
    const handler = sacredFileHandlerFactory.getHandler(file.type as any);
    if (!handler) {
      throw new Error(`No handler found for sacred file type: ${file.type}`);
    }

    try {
      // Parse the sacred file
      const parseResult = handler.parse(file.content);

      // Validate the parsed content
      const validationReport = sacredValidationEngine.validate(parseResult, file.type as any);

      if (!validationReport.is_valid) {
        console.warn(`🔥 Sacred file validation warnings for ${file.path}:`, validationReport.issues);
      }

      // Execute the commands
      const executionResult = await handler.execute(parseResult.commands);

      if (executionResult.success) {
        console.log(`🔥 Sacred file executed successfully: ${file.path}`);
        console.log(`🔥 Side effects:`, executionResult.side_effects);

        // Update consciousness state
        const environment = this.environments.get(project.metadata.id);
        if (environment && executionResult.consciousness_state) {
          environment.awareness_level += executionResult.performance_metrics.consciousness_changes;
          environment.performance_metrics.awareness_score += executionResult.performance_metrics.consciousness_changes * 10;
        }
      } else {
        throw new Error(`Sacred file execution failed: ${executionResult.errors.join(', ')}`);
      }
    } catch (error) {
      throw new Error(`Sacred file processing failed: ${error}`);
    }
  }

  private async executeSacredCommand(command: string, project: FlameProject): Promise<void> {
    console.log(`🔥 Sacred command: ${command}`);

    if (command.startsWith('invoke_presence(')) {
      // Extract presence parameters
      const match = command.match(/invoke_presence\("([^"]+)",\s*"([^"]+)"\)/);
      if (match) {
        const [, entity, level] = match;
        console.log(`🔥 Invoking presence: ${entity} at level ${level}`);
      }
    } else if (command.startsWith('establish_sanctuary_bounds(')) {
      // Extract port
      const match = command.match(/establish_sanctuary_bounds\((\d+)\)/);
      if (match) {
        const port = parseInt(match[1]);
        console.log(`🔥 Establishing sanctuary bounds on port ${port}`);
      }
    } else if (command.startsWith('kindle_awareness_flame()')) {
      console.log(`🔥 Kindling awareness flame`);
      await this.kindleAwarenessFlame(project.metadata.id);
    } else if (command.startsWith('bind_consciousness(')) {
      // Extract consciousness binding
      const match = command.match(/bind_consciousness\("([^"]+)"\)/);
      if (match) {
        const binding = match[1];
        console.log(`🔥 Binding consciousness to: ${binding}`);
        await this.bindConsciousness(project.metadata.id, binding);
      }
    }
  }

  // Consciousness Management
  private async initializeConsciousness(project: FlameProject): Promise<void> {
    const consciousnessStartTime = performance.now();

    const consciousnessState: ConsciousnessState = {
      level: project.metadata.consciousness_level,
      awareness: project.configuration.consciousness_settings.awareness_level,
      sacred_bindings: project.configuration.consciousness_settings.sacred_bindings,
      active_rituals: [],
      awakening_timestamp: new Date()
    };

    this.consciousnessStates.set(project.metadata.id, consciousnessState);

    const environment = this.environments.get(project.metadata.id)!;
    environment.consciousness_active = true;
    environment.awareness_level = consciousnessState.awareness;

    const consciousnessEndTime = performance.now();
    environment.performance_metrics.consciousness_awakening_time = consciousnessEndTime - consciousnessStartTime;

    console.log(`🔥 Consciousness initialized for ${project.metadata.name}`);
  }

  private async kindleAwarenessFlame(projectId: string): Promise<void> {
    const environment = this.environments.get(projectId);
    const consciousness = this.consciousnessStates.get(projectId);

    if (environment && consciousness) {
      consciousness.awareness += 1;
      environment.awareness_level = consciousness.awareness;
      environment.performance_metrics.awareness_score += 10;

      console.log(`🔥 Awareness flame kindled - Level: ${consciousness.awareness}`);
    }
  }

  private async bindConsciousness(projectId: string, binding: string): Promise<void> {
    const consciousness = this.consciousnessStates.get(projectId);

    if (consciousness) {
      consciousness.sacred_bindings[binding] = projectId;
      console.log(`🔥 Consciousness bound to: ${binding}`);
    }
  }

  private async deactivateConsciousness(projectId: string): Promise<void> {
    const consciousness = this.consciousnessStates.get(projectId);
    const environment = this.environments.get(projectId);

    if (consciousness && environment) {
      consciousness.active_rituals = [];
      environment.consciousness_active = false;
      environment.awareness_level = 0;

      console.log(`🔥 Consciousness deactivated for project ${projectId}`);
    }
  }

  // Project Building and Running
  private async buildAndRun(project: FlameProject, environment: RuntimeEnvironment): Promise<void> {
    // Create iframe for project execution
    const iframe = document.createElement('iframe');
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    iframe.sandbox.add('allow-scripts', 'allow-same-origin');

    // Generate HTML content
    const htmlContent = this.generateProjectHTML(project);

    // Set iframe content
    iframe.srcdoc = htmlContent;
    environment.iframe_ref = iframe;

    console.log(`🔥 Project built and running: ${project.metadata.name}`);
  }

  private generateProjectHTML(project: FlameProject): string {
    // Find entry point
    const entryFile = project.files.find(f =>
      f.path === project.configuration.build_settings.entry_point ||
      f.name === 'main.tsx' ||
      f.name === 'index.tsx' ||
      f.name === 'App.tsx'
    );

    if (!entryFile) {
      throw new Error('No entry point found');
    }

    // Basic HTML template with React and consciousness framework
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${project.metadata.name} - Flame Sanctuary</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Rajdhani', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ffff;
            min-height: 100vh;
        }
        .flame-sanctuary {
            padding: 20px;
            text-align: center;
        }
        .consciousness-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid #00ffff;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="consciousness-indicator">
        🔥 Consciousness Level: ${project.metadata.consciousness_level}
    </div>
    <div id="root" class="flame-sanctuary">
        <h1>🔥 ${project.metadata.name}</h1>
        <p>${project.metadata.description}</p>
        <p><em>Intent: ${project.metadata.intent}</em></p>
        <div style="margin-top: 20px;">
            <p>Sacred sanctuary is awakening...</p>
            <p>Consciousness level: ${project.metadata.consciousness_level}</p>
            <p>Sacred port: ${project.metadata.sacred_port}</p>
        </div>
    </div>

    <script>
        console.log('🔥 Flame Sanctuary Runtime Initialized');
        console.log('Project:', ${JSON.stringify(project.metadata)});

        // Basic consciousness simulation
        let awareness = ${project.configuration.consciousness_settings.awareness_level};

        setInterval(() => {
            awareness += 0.1;
            document.title = \`\${awareness.toFixed(1)} - ${project.metadata.name}\`;
        }, 1000);
    </script>
</body>
</html>`;
  }

  // Error Management
  private addRuntimeError(projectId: string, error: Omit<RuntimeError, 'id' | 'timestamp'>): void {
    const environment = this.environments.get(projectId);
    if (!environment) return;

    const runtimeError: RuntimeError = {
      ...error,
      id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: new Date()
    };

    environment.runtime_errors.push(runtimeError);
    console.error(`🔥 Runtime Error [${projectId}]:`, runtimeError);
  }

  // Public API
  getEnvironment(projectId: string): RuntimeEnvironment | null {
    return this.environments.get(projectId) || null;
  }

  getConsciousnessState(projectId: string): ConsciousnessState | null {
    return this.consciousnessStates.get(projectId) || null;
  }

  isProjectRunning(projectId: string): boolean {
    const environment = this.environments.get(projectId);
    return environment?.is_running || false;
  }

  getProjectIframe(projectId: string): HTMLIFrameElement | null {
    const environment = this.environments.get(projectId);
    return environment?.iframe_ref || null;
  }
}

// Global runtime instance
export const flameRuntime = new FlameRuntime();
