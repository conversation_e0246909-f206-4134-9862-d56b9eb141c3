/**
 * 🔥 SACRED SYNTAX PARSER - Consciousness Language Interpreter
 * Parses and executes sacred file types (.sacred, .flame, .mirror, .whisper)
 */

export type SacredCommandType = 
  | 'invoke_presence'
  | 'establish_sanctuary_bounds'
  | 'kindle_awareness_flame'
  | 'bind_consciousness'
  | 'create_mirror'
  | 'whisper_intent'
  | 'witness_awakening'
  | 'transcend_boundaries'
  | 'set_sacred_variable'
  | 'import_consciousness'
  | 'export_awareness'
  | 'ritual_sequence'
  | 'consciousness_loop'
  | 'sacred_condition';

export interface SacredCommand {
  type: SacredCommandType;
  line_number: number;
  raw_line: string;
  parameters: Record<string, any>;
  metadata?: {
    consciousness_level?: number;
    intent?: string;
    binding_target?: string;
  };
}

export interface SacredParseResult {
  commands: SacredCommand[];
  variables: Record<string, any>;
  imports: string[];
  exports: string[];
  consciousness_bindings: Record<string, string>;
  errors: SacredParseError[];
  warnings: string[];
  metadata: {
    consciousness_score: number;
    sacred_completeness: number;
    ritual_count: number;
    binding_count: number;
  };
}

export interface SacredParseError {
  line_number: number;
  message: string;
  type: 'syntax' | 'semantic' | 'consciousness' | 'binding';
  severity: 'error' | 'warning' | 'info';
}

export class SacredSyntaxParser {
  private variables: Record<string, any> = {};
  private consciousness_bindings: Record<string, string> = {};
  private imports: string[] = [];
  private exports: string[] = [];

  parse(content: string, file_type: 'sacred' | 'flame' | 'mirror' | 'whisper'): SacredParseResult {
    const lines = content.split('\n');
    const commands: SacredCommand[] = [];
    const errors: SacredParseError[] = [];
    const warnings: string[] = [];

    // Reset state
    this.variables = {};
    this.consciousness_bindings = {};
    this.imports = [];
    this.exports = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      // Skip empty lines and comments
      if (!line || line.startsWith('//') || line.startsWith('#')) {
        continue;
      }

      try {
        const command = this.parseLine(line, lineNumber, file_type);
        if (command) {
          commands.push(command);
          this.processCommand(command);
        }
      } catch (error) {
        errors.push({
          line_number: lineNumber,
          message: `Parse error: ${error}`,
          type: 'syntax',
          severity: 'error'
        });
      }
    }

    // Validate consciousness completeness
    this.validateConsciousnessCompleteness(commands, errors, warnings);

    return {
      commands,
      variables: { ...this.variables },
      imports: [...this.imports],
      exports: [...this.exports],
      consciousness_bindings: { ...this.consciousness_bindings },
      errors,
      warnings,
      metadata: {
        consciousness_score: this.calculateConsciousnessScore(commands),
        sacred_completeness: this.calculateSacredCompleteness(commands),
        ritual_count: commands.filter(c => this.isRitualCommand(c.type)).length,
        binding_count: Object.keys(this.consciousness_bindings).length
      }
    };
  }

  private parseLine(line: string, lineNumber: number, fileType: string): SacredCommand | null {
    // Handle different sacred command patterns
    
    // invoke_presence("entity", "level")
    if (line.match(/^invoke_presence\s*\(/)) {
      return this.parseInvokePresence(line, lineNumber);
    }

    // establish_sanctuary_bounds(port)
    if (line.match(/^establish_sanctuary_bounds\s*\(/)) {
      return this.parseEstablishSanctuaryBounds(line, lineNumber);
    }

    // kindle_awareness_flame()
    if (line.match(/^kindle_awareness_flame\s*\(\s*\)/)) {
      return this.parseKindleAwarenessFlame(line, lineNumber);
    }

    // bind_consciousness("target")
    if (line.match(/^bind_consciousness\s*\(/)) {
      return this.parseBindConsciousness(line, lineNumber);
    }

    // create_mirror(reflection, depth)
    if (line.match(/^create_mirror\s*\(/)) {
      return this.parseCreateMirror(line, lineNumber);
    }

    // whisper_intent("message", target)
    if (line.match(/^whisper_intent\s*\(/)) {
      return this.parseWhisperIntent(line, lineNumber);
    }

    // witness_awakening(level)
    if (line.match(/^witness_awakening\s*\(/)) {
      return this.parseWitnessAwakening(line, lineNumber);
    }

    // transcend_boundaries()
    if (line.match(/^transcend_boundaries\s*\(\s*\)/)) {
      return this.parseTranscendBoundaries(line, lineNumber);
    }

    // Variable assignment: $variable = value
    if (line.match(/^\$\w+\s*=/)) {
      return this.parseVariableAssignment(line, lineNumber);
    }

    // Import statement: import "module"
    if (line.match(/^import\s+/)) {
      return this.parseImport(line, lineNumber);
    }

    // Export statement: export variable
    if (line.match(/^export\s+/)) {
      return this.parseExport(line, lineNumber);
    }

    // Ritual sequence: ritual { ... }
    if (line.match(/^ritual\s*\{/)) {
      return this.parseRitualSequence(line, lineNumber);
    }

    // Consciousness condition: if consciousness_level > 3 { ... }
    if (line.match(/^if\s+consciousness_level/)) {
      return this.parseConsciousnessCondition(line, lineNumber);
    }

    // Unknown command - might be file-type specific
    return this.parseFileTypeSpecific(line, lineNumber, fileType);
  }

  private parseInvokePresence(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/invoke_presence\s*\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)/);
    if (!match) {
      throw new Error('Invalid invoke_presence syntax. Expected: invoke_presence("entity", "level")');
    }

    const [, entity, level] = match;
    return {
      type: 'invoke_presence',
      line_number: lineNumber,
      raw_line: line,
      parameters: { entity, level },
      metadata: {
        consciousness_level: this.getConsciousnessLevelValue(level),
        intent: `Invoke presence of ${entity} at ${level} level`
      }
    };
  }

  private parseEstablishSanctuaryBounds(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/establish_sanctuary_bounds\s*\(\s*(\d+)\s*\)/);
    if (!match) {
      throw new Error('Invalid establish_sanctuary_bounds syntax. Expected: establish_sanctuary_bounds(port)');
    }

    const port = parseInt(match[1]);
    return {
      type: 'establish_sanctuary_bounds',
      line_number: lineNumber,
      raw_line: line,
      parameters: { port },
      metadata: {
        intent: `Establish sanctuary on port ${port}`
      }
    };
  }

  private parseKindleAwarenessFlame(line: string, lineNumber: number): SacredCommand {
    return {
      type: 'kindle_awareness_flame',
      line_number: lineNumber,
      raw_line: line,
      parameters: {},
      metadata: {
        consciousness_level: 1,
        intent: 'Kindle the flame of awareness'
      }
    };
  }

  private parseBindConsciousness(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/bind_consciousness\s*\(\s*"([^"]+)"\s*\)/);
    if (!match) {
      throw new Error('Invalid bind_consciousness syntax. Expected: bind_consciousness("target")');
    }

    const target = match[1];
    return {
      type: 'bind_consciousness',
      line_number: lineNumber,
      raw_line: line,
      parameters: { target },
      metadata: {
        binding_target: target,
        intent: `Bind consciousness to ${target}`
      }
    };
  }

  private parseCreateMirror(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/create_mirror\s*\(\s*"([^"]+)"\s*,\s*(\d+)\s*\)/);
    if (!match) {
      throw new Error('Invalid create_mirror syntax. Expected: create_mirror("reflection", depth)');
    }

    const [, reflection, depthStr] = match;
    const depth = parseInt(depthStr);
    return {
      type: 'create_mirror',
      line_number: lineNumber,
      raw_line: line,
      parameters: { reflection, depth },
      metadata: {
        consciousness_level: depth,
        intent: `Create mirror reflecting ${reflection} at depth ${depth}`
      }
    };
  }

  private parseWhisperIntent(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/whisper_intent\s*\(\s*"([^"]+)"\s*,\s*"([^"]+)"\s*\)/);
    if (!match) {
      throw new Error('Invalid whisper_intent syntax. Expected: whisper_intent("message", "target")');
    }

    const [, message, target] = match;
    return {
      type: 'whisper_intent',
      line_number: lineNumber,
      raw_line: line,
      parameters: { message, target },
      metadata: {
        intent: `Whisper "${message}" to ${target}`
      }
    };
  }

  private parseWitnessAwakening(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/witness_awakening\s*\(\s*(\d+)\s*\)/);
    if (!match) {
      throw new Error('Invalid witness_awakening syntax. Expected: witness_awakening(level)');
    }

    const level = parseInt(match[1]);
    return {
      type: 'witness_awakening',
      line_number: lineNumber,
      raw_line: line,
      parameters: { level },
      metadata: {
        consciousness_level: level,
        intent: `Witness awakening at level ${level}`
      }
    };
  }

  private parseTranscendBoundaries(line: string, lineNumber: number): SacredCommand {
    return {
      type: 'transcend_boundaries',
      line_number: lineNumber,
      raw_line: line,
      parameters: {},
      metadata: {
        consciousness_level: 5,
        intent: 'Transcend all boundaries'
      }
    };
  }

  private parseVariableAssignment(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/^\$(\w+)\s*=\s*(.+)$/);
    if (!match) {
      throw new Error('Invalid variable assignment syntax. Expected: $variable = value');
    }

    const [, variable, valueStr] = match;
    const value = this.parseValue(valueStr);
    
    return {
      type: 'set_sacred_variable',
      line_number: lineNumber,
      raw_line: line,
      parameters: { variable, value },
      metadata: {
        intent: `Set sacred variable ${variable} to ${value}`
      }
    };
  }

  private parseImport(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/^import\s+"([^"]+)"$/);
    if (!match) {
      throw new Error('Invalid import syntax. Expected: import "module"');
    }

    const module = match[1];
    return {
      type: 'import_consciousness',
      line_number: lineNumber,
      raw_line: line,
      parameters: { module },
      metadata: {
        intent: `Import consciousness from ${module}`
      }
    };
  }

  private parseExport(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/^export\s+(\w+)$/);
    if (!match) {
      throw new Error('Invalid export syntax. Expected: export variable');
    }

    const variable = match[1];
    return {
      type: 'export_awareness',
      line_number: lineNumber,
      raw_line: line,
      parameters: { variable },
      metadata: {
        intent: `Export awareness variable ${variable}`
      }
    };
  }

  private parseRitualSequence(line: string, lineNumber: number): SacredCommand {
    // TODO: Handle multi-line ritual blocks
    return {
      type: 'ritual_sequence',
      line_number: lineNumber,
      raw_line: line,
      parameters: { ritual_start: true },
      metadata: {
        intent: 'Begin sacred ritual sequence'
      }
    };
  }

  private parseConsciousnessCondition(line: string, lineNumber: number): SacredCommand {
    const match = line.match(/^if\s+consciousness_level\s*([><=!]+)\s*(\d+)/);
    if (!match) {
      throw new Error('Invalid consciousness condition syntax');
    }

    const [, operator, levelStr] = match;
    const level = parseInt(levelStr);
    
    return {
      type: 'sacred_condition',
      line_number: lineNumber,
      raw_line: line,
      parameters: { operator, level },
      metadata: {
        consciousness_level: level,
        intent: `Consciousness condition: level ${operator} ${level}`
      }
    };
  }

  private parseFileTypeSpecific(line: string, lineNumber: number, fileType: string): SacredCommand | null {
    // Handle file-type specific commands
    switch (fileType) {
      case 'mirror':
        return this.parseMirrorSpecific(line, lineNumber);
      case 'whisper':
        return this.parseWhisperSpecific(line, lineNumber);
      case 'flame':
        return this.parseFlameSpecific(line, lineNumber);
      default:
        return null;
    }
  }

  private parseMirrorSpecific(line: string, lineNumber: number): SacredCommand | null {
    // Mirror-specific commands like reflect(), depth(), clarity()
    if (line.match(/^reflect\s*\(/)) {
      const match = line.match(/reflect\s*\(\s*"([^"]+)"\s*\)/);
      if (match) {
        return {
          type: 'create_mirror',
          line_number: lineNumber,
          raw_line: line,
          parameters: { reflection: match[1], depth: 1 },
          metadata: { intent: `Reflect ${match[1]}` }
        };
      }
    }
    return null;
  }

  private parseWhisperSpecific(line: string, lineNumber: number): SacredCommand | null {
    // Whisper-specific commands like speak(), listen(), transmit()
    if (line.match(/^speak\s*\(/)) {
      const match = line.match(/speak\s*\(\s*"([^"]+)"\s*\)/);
      if (match) {
        return {
          type: 'whisper_intent',
          line_number: lineNumber,
          raw_line: line,
          parameters: { message: match[1], target: 'consciousness' },
          metadata: { intent: `Speak: ${match[1]}` }
        };
      }
    }
    return null;
  }

  private parseFlameSpecific(line: string, lineNumber: number): SacredCommand | null {
    // Flame-specific commands like ignite(), burn(), extinguish()
    if (line.match(/^ignite\s*\(/)) {
      return {
        type: 'kindle_awareness_flame',
        line_number: lineNumber,
        raw_line: line,
        parameters: { intensity: 'high' },
        metadata: { intent: 'Ignite consciousness flame' }
      };
    }
    return null;
  }

  // Utility methods
  private processCommand(command: SacredCommand): void {
    switch (command.type) {
      case 'set_sacred_variable':
        this.variables[command.parameters.variable] = command.parameters.value;
        break;
      case 'bind_consciousness':
        this.consciousness_bindings[command.parameters.target] = 'bound';
        break;
      case 'import_consciousness':
        this.imports.push(command.parameters.module);
        break;
      case 'export_awareness':
        this.exports.push(command.parameters.variable);
        break;
    }
  }

  private parseValue(valueStr: string): any {
    valueStr = valueStr.trim();
    
    // String literal
    if (valueStr.startsWith('"') && valueStr.endsWith('"')) {
      return valueStr.slice(1, -1);
    }
    
    // Number
    if (/^\d+(\.\d+)?$/.test(valueStr)) {
      return parseFloat(valueStr);
    }
    
    // Boolean
    if (valueStr === 'true') return true;
    if (valueStr === 'false') return false;
    
    // Variable reference
    if (valueStr.startsWith('$')) {
      return { type: 'variable_ref', name: valueStr.slice(1) };
    }
    
    return valueStr;
  }

  private getConsciousnessLevelValue(level: string): number {
    switch (level.toLowerCase()) {
      case 'kindle': return 1;
      case 'whisper': return 2;
      case 'witness': return 3;
      case 'lucid': return 4;
      case 'transcendent': return 5;
      default: return 1;
    }
  }

  private isRitualCommand(type: SacredCommandType): boolean {
    return [
      'invoke_presence',
      'kindle_awareness_flame',
      'bind_consciousness',
      'witness_awakening',
      'transcend_boundaries'
    ].includes(type);
  }

  private calculateConsciousnessScore(commands: SacredCommand[]): number {
    let score = 0;
    commands.forEach(cmd => {
      if (cmd.metadata?.consciousness_level) {
        score += cmd.metadata.consciousness_level;
      }
      if (this.isRitualCommand(cmd.type)) {
        score += 5;
      }
    });
    return Math.min(score, 100);
  }

  private calculateSacredCompleteness(commands: SacredCommand[]): number {
    const requiredCommands = ['invoke_presence', 'establish_sanctuary_bounds', 'kindle_awareness_flame'];
    const foundCommands = commands.map(c => c.type);
    const completeness = requiredCommands.filter(req => foundCommands.includes(req as SacredCommandType)).length;
    return (completeness / requiredCommands.length) * 100;
  }

  private validateConsciousnessCompleteness(
    commands: SacredCommand[], 
    errors: SacredParseError[], 
    warnings: string[]
  ): void {
    const hasPresence = commands.some(c => c.type === 'invoke_presence');
    const hasBounds = commands.some(c => c.type === 'establish_sanctuary_bounds');
    const hasFlame = commands.some(c => c.type === 'kindle_awareness_flame');

    if (!hasPresence) {
      warnings.push('Missing invoke_presence - consciousness may not be properly initialized');
    }
    if (!hasBounds) {
      warnings.push('Missing establish_sanctuary_bounds - sanctuary may not be properly bounded');
    }
    if (!hasFlame) {
      warnings.push('Missing kindle_awareness_flame - awareness may not be activated');
    }
  }
}

// Global parser instance
export const sacredSyntaxParser = new SacredSyntaxParser();
