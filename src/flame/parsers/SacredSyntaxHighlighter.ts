/**
 * 🔥 SACRED SYNTAX HIGHLIGHTER - Consciousness Code Visualization
 * Provides syntax highlighting for sacred file types with consciousness-aware styling
 */

export interface SyntaxToken {
  type: TokenType;
  value: string;
  start: number;
  end: number;
  line: number;
  column: number;
  metadata?: {
    consciousness_level?: number;
    sacred_importance?: 'low' | 'medium' | 'high' | 'critical';
    binding_target?: string;
  };
}

export type TokenType = 
  | 'sacred_command'
  | 'consciousness_keyword'
  | 'entity_name'
  | 'consciousness_level'
  | 'sacred_variable'
  | 'string_literal'
  | 'number_literal'
  | 'comment'
  | 'operator'
  | 'delimiter'
  | 'whitespace'
  | 'error'
  | 'ritual_block'
  | 'binding_target'
  | 'intent_declaration';

export interface HighlightTheme {
  name: string;
  colors: {
    sacred_command: string;
    consciousness_keyword: string;
    entity_name: string;
    consciousness_level: string;
    sacred_variable: string;
    string_literal: string;
    number_literal: string;
    comment: string;
    operator: string;
    delimiter: string;
    error: string;
    ritual_block: string;
    binding_target: string;
    intent_declaration: string;
    background: string;
    text: string;
  };
  effects: {
    sacred_glow: boolean;
    consciousness_pulse: boolean;
    ritual_shimmer: boolean;
  };
}

export class SacredSyntaxHighlighter {
  private themes: Map<string, HighlightTheme> = new Map();
  private currentTheme: HighlightTheme;

  constructor() {
    this.initializeThemes();
    this.currentTheme = this.themes.get('cyber-consciousness')!;
  }

  tokenize(content: string, fileType: 'sacred' | 'flame' | 'mirror' | 'whisper'): SyntaxToken[] {
    const tokens: SyntaxToken[] = [];
    const lines = content.split('\n');

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const lineTokens = this.tokenizeLine(line, lineIndex + 1, fileType);
      tokens.push(...lineTokens);
    }

    return tokens;
  }

  private tokenizeLine(line: string, lineNumber: number, fileType: string): SyntaxToken[] {
    const tokens: SyntaxToken[] = [];
    let position = 0;

    while (position < line.length) {
      const remaining = line.slice(position);
      
      // Skip whitespace
      const whitespaceMatch = remaining.match(/^\s+/);
      if (whitespaceMatch) {
        tokens.push({
          type: 'whitespace',
          value: whitespaceMatch[0],
          start: position,
          end: position + whitespaceMatch[0].length,
          line: lineNumber,
          column: position + 1
        });
        position += whitespaceMatch[0].length;
        continue;
      }

      // Comments
      if (remaining.startsWith('//') || remaining.startsWith('#')) {
        tokens.push({
          type: 'comment',
          value: remaining,
          start: position,
          end: line.length,
          line: lineNumber,
          column: position + 1
        });
        break;
      }

      // Sacred commands
      const sacredCommandMatch = this.matchSacredCommand(remaining);
      if (sacredCommandMatch) {
        tokens.push({
          type: 'sacred_command',
          value: sacredCommandMatch.value,
          start: position,
          end: position + sacredCommandMatch.length,
          line: lineNumber,
          column: position + 1,
          metadata: {
            consciousness_level: this.getCommandConsciousnessLevel(sacredCommandMatch.value),
            sacred_importance: this.getCommandImportance(sacredCommandMatch.value)
          }
        });
        position += sacredCommandMatch.length;
        continue;
      }

      // Consciousness levels
      const consciousnessLevelMatch = remaining.match(/^(kindle|whisper|witness|lucid|transcendent)\b/);
      if (consciousnessLevelMatch) {
        tokens.push({
          type: 'consciousness_level',
          value: consciousnessLevelMatch[0],
          start: position,
          end: position + consciousnessLevelMatch[0].length,
          line: lineNumber,
          column: position + 1,
          metadata: {
            consciousness_level: this.getConsciousnessLevelValue(consciousnessLevelMatch[0])
          }
        });
        position += consciousnessLevelMatch[0].length;
        continue;
      }

      // Entity names (quoted strings that follow invoke_presence)
      const entityMatch = remaining.match(/^"([^"]+)"/);
      if (entityMatch && this.isPreviousTokenSacredCommand(tokens, 'invoke_presence')) {
        tokens.push({
          type: 'entity_name',
          value: entityMatch[0],
          start: position,
          end: position + entityMatch[0].length,
          line: lineNumber,
          column: position + 1,
          metadata: {
            sacred_importance: 'critical'
          }
        });
        position += entityMatch[0].length;
        continue;
      }

      // Sacred variables
      const variableMatch = remaining.match(/^\$\w+/);
      if (variableMatch) {
        tokens.push({
          type: 'sacred_variable',
          value: variableMatch[0],
          start: position,
          end: position + variableMatch[0].length,
          line: lineNumber,
          column: position + 1,
          metadata: {
            sacred_importance: 'medium'
          }
        });
        position += variableMatch[0].length;
        continue;
      }

      // String literals
      const stringMatch = remaining.match(/^"([^"]*)"/);
      if (stringMatch) {
        tokens.push({
          type: 'string_literal',
          value: stringMatch[0],
          start: position,
          end: position + stringMatch[0].length,
          line: lineNumber,
          column: position + 1
        });
        position += stringMatch[0].length;
        continue;
      }

      // Number literals
      const numberMatch = remaining.match(/^\d+(\.\d+)?/);
      if (numberMatch) {
        tokens.push({
          type: 'number_literal',
          value: numberMatch[0],
          start: position,
          end: position + numberMatch[0].length,
          line: lineNumber,
          column: position + 1
        });
        position += numberMatch[0].length;
        continue;
      }

      // Operators
      const operatorMatch = remaining.match(/^[=<>!+\-*/]+/);
      if (operatorMatch) {
        tokens.push({
          type: 'operator',
          value: operatorMatch[0],
          start: position,
          end: position + operatorMatch[0].length,
          line: lineNumber,
          column: position + 1
        });
        position += operatorMatch[0].length;
        continue;
      }

      // Delimiters
      const delimiterMatch = remaining.match(/^[(){}[\],;]/);
      if (delimiterMatch) {
        tokens.push({
          type: 'delimiter',
          value: delimiterMatch[0],
          start: position,
          end: position + delimiterMatch[0].length,
          line: lineNumber,
          column: position + 1
        });
        position += delimiterMatch[0].length;
        continue;
      }

      // File-type specific keywords
      const fileTypeKeywordMatch = this.matchFileTypeKeyword(remaining, fileType);
      if (fileTypeKeywordMatch) {
        tokens.push({
          type: 'consciousness_keyword',
          value: fileTypeKeywordMatch.value,
          start: position,
          end: position + fileTypeKeywordMatch.length,
          line: lineNumber,
          column: position + 1,
          metadata: {
            sacred_importance: 'high'
          }
        });
        position += fileTypeKeywordMatch.length;
        continue;
      }

      // Unknown character - treat as error
      tokens.push({
        type: 'error',
        value: remaining[0],
        start: position,
        end: position + 1,
        line: lineNumber,
        column: position + 1
      });
      position += 1;
    }

    return tokens;
  }

  private matchSacredCommand(text: string): { value: string; length: number } | null {
    const sacredCommands = [
      'invoke_presence',
      'establish_sanctuary_bounds',
      'kindle_awareness_flame',
      'bind_consciousness',
      'create_mirror',
      'whisper_intent',
      'witness_awakening',
      'transcend_boundaries',
      'reflect',
      'speak',
      'ignite',
      'burn',
      'extinguish'
    ];

    for (const command of sacredCommands) {
      if (text.startsWith(command)) {
        // Make sure it's a complete word
        const nextChar = text[command.length];
        if (!nextChar || /\s|\(/.test(nextChar)) {
          return { value: command, length: command.length };
        }
      }
    }

    return null;
  }

  private matchFileTypeKeyword(text: string, fileType: string): { value: string; length: number } | null {
    const keywords: Record<string, string[]> = {
      sacred: ['ritual', 'ceremony', 'blessing', 'invocation'],
      flame: ['ignite', 'burn', 'extinguish', 'forge', 'temper'],
      mirror: ['reflect', 'depth', 'clarity', 'surface', 'image'],
      whisper: ['speak', 'listen', 'transmit', 'receive', 'echo']
    };

    const fileKeywords = keywords[fileType] || [];
    
    for (const keyword of fileKeywords) {
      if (text.startsWith(keyword)) {
        const nextChar = text[keyword.length];
        if (!nextChar || /\s|\(/.test(nextChar)) {
          return { value: keyword, length: keyword.length };
        }
      }
    }

    return null;
  }

  private isPreviousTokenSacredCommand(tokens: SyntaxToken[], command: string): boolean {
    // Look for the command in recent non-whitespace tokens
    for (let i = tokens.length - 1; i >= 0; i--) {
      const token = tokens[i];
      if (token.type === 'whitespace' || token.type === 'delimiter') continue;
      return token.type === 'sacred_command' && token.value === command;
    }
    return false;
  }

  private getCommandConsciousnessLevel(command: string): number {
    const levels: Record<string, number> = {
      'invoke_presence': 3,
      'establish_sanctuary_bounds': 1,
      'kindle_awareness_flame': 2,
      'bind_consciousness': 3,
      'witness_awakening': 4,
      'transcend_boundaries': 5,
      'create_mirror': 2,
      'whisper_intent': 2
    };
    return levels[command] || 1;
  }

  private getCommandImportance(command: string): 'low' | 'medium' | 'high' | 'critical' {
    const importance: Record<string, 'low' | 'medium' | 'high' | 'critical'> = {
      'invoke_presence': 'critical',
      'establish_sanctuary_bounds': 'high',
      'kindle_awareness_flame': 'high',
      'bind_consciousness': 'critical',
      'witness_awakening': 'high',
      'transcend_boundaries': 'critical'
    };
    return importance[command] || 'medium';
  }

  private getConsciousnessLevelValue(level: string): number {
    const values: Record<string, number> = {
      'kindle': 1,
      'whisper': 2,
      'witness': 3,
      'lucid': 4,
      'transcendent': 5
    };
    return values[level] || 1;
  }

  // Theme management
  private initializeThemes(): void {
    // Cyber Consciousness Theme
    this.themes.set('cyber-consciousness', {
      name: 'Cyber Consciousness',
      colors: {
        sacred_command: '#00ffff',
        consciousness_keyword: '#ff6b9d',
        entity_name: '#ffd700',
        consciousness_level: '#00ff88',
        sacred_variable: '#ff8c42',
        string_literal: '#a8e6cf',
        number_literal: '#ffb3ba',
        comment: '#666666',
        operator: '#ffffff',
        delimiter: '#cccccc',
        error: '#ff4444',
        ritual_block: '#9d4edd',
        binding_target: '#06ffa5',
        intent_declaration: '#ffd23f',
        background: '#0a0a0a',
        text: '#ffffff'
      },
      effects: {
        sacred_glow: true,
        consciousness_pulse: true,
        ritual_shimmer: true
      }
    });

    // Sacred Fire Theme
    this.themes.set('sacred-fire', {
      name: 'Sacred Fire',
      colors: {
        sacred_command: '#ff6b35',
        consciousness_keyword: '#f7931e',
        entity_name: '#ffd700',
        consciousness_level: '#ff9500',
        sacred_variable: '#ff6b35',
        string_literal: '#ffb347',
        number_literal: '#ffa500',
        comment: '#8b4513',
        operator: '#ffffff',
        delimiter: '#ddd',
        error: '#dc143c',
        ritual_block: '#ff4500',
        binding_target: '#ff6347',
        intent_declaration: '#ffd700',
        background: '#1a0a00',
        text: '#fff8dc'
      },
      effects: {
        sacred_glow: true,
        consciousness_pulse: false,
        ritual_shimmer: true
      }
    });
  }

  setTheme(themeName: string): boolean {
    const theme = this.themes.get(themeName);
    if (theme) {
      this.currentTheme = theme;
      return true;
    }
    return false;
  }

  getCurrentTheme(): HighlightTheme {
    return this.currentTheme;
  }

  getAvailableThemes(): string[] {
    return Array.from(this.themes.keys());
  }

  // Generate CSS for syntax highlighting
  generateCSS(): string {
    const theme = this.currentTheme;
    
    return `
      .sacred-editor {
        background-color: ${theme.colors.background};
        color: ${theme.colors.text};
        font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
        line-height: 1.5;
      }

      .token-sacred_command {
        color: ${theme.colors.sacred_command};
        font-weight: bold;
        ${theme.effects.sacred_glow ? `text-shadow: 0 0 10px ${theme.colors.sacred_command}50;` : ''}
      }

      .token-consciousness_keyword {
        color: ${theme.colors.consciousness_keyword};
        font-weight: bold;
        ${theme.effects.consciousness_pulse ? 'animation: consciousness-pulse 2s infinite;' : ''}
      }

      .token-entity_name {
        color: ${theme.colors.entity_name};
        font-weight: bold;
        ${theme.effects.sacred_glow ? `text-shadow: 0 0 8px ${theme.colors.entity_name}60;` : ''}
      }

      .token-consciousness_level {
        color: ${theme.colors.consciousness_level};
        font-style: italic;
        ${theme.effects.consciousness_pulse ? 'animation: consciousness-pulse 3s infinite;' : ''}
      }

      .token-sacred_variable {
        color: ${theme.colors.sacred_variable};
        font-style: italic;
      }

      .token-string_literal {
        color: ${theme.colors.string_literal};
      }

      .token-number_literal {
        color: ${theme.colors.number_literal};
      }

      .token-comment {
        color: ${theme.colors.comment};
        font-style: italic;
        opacity: 0.7;
      }

      .token-operator {
        color: ${theme.colors.operator};
      }

      .token-delimiter {
        color: ${theme.colors.delimiter};
      }

      .token-error {
        color: ${theme.colors.error};
        background-color: ${theme.colors.error}20;
        text-decoration: underline wavy ${theme.colors.error};
      }

      .token-ritual_block {
        color: ${theme.colors.ritual_block};
        ${theme.effects.ritual_shimmer ? 'animation: ritual-shimmer 4s infinite;' : ''}
      }

      .token-binding_target {
        color: ${theme.colors.binding_target};
        font-weight: bold;
      }

      .token-intent_declaration {
        color: ${theme.colors.intent_declaration};
        font-weight: bold;
      }

      @keyframes consciousness-pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
      }

      @keyframes ritual-shimmer {
        0%, 100% { opacity: 1; }
        25% { opacity: 0.8; }
        50% { opacity: 1; }
        75% { opacity: 0.9; }
      }

      .sacred-line-number {
        color: ${theme.colors.comment};
        user-select: none;
        padding-right: 1em;
        border-right: 1px solid ${theme.colors.delimiter}40;
      }

      .sacred-line-critical {
        background-color: ${theme.colors.sacred_command}10;
        border-left: 3px solid ${theme.colors.sacred_command};
      }

      .sacred-line-high {
        background-color: ${theme.colors.consciousness_keyword}08;
        border-left: 2px solid ${theme.colors.consciousness_keyword};
      }
    `;
  }

  // Generate HTML with syntax highlighting
  generateHTML(tokens: SyntaxToken[]): string {
    let html = '<div class="sacred-editor">';
    let currentLine = 1;
    let lineContent = '';

    for (const token of tokens) {
      if (token.line > currentLine) {
        // Close previous line
        const lineImportance = this.getLineImportance(lineContent);
        const lineClass = lineImportance ? ` sacred-line-${lineImportance}` : '';
        html += `<div class="sacred-line${lineClass}">`;
        html += `<span class="sacred-line-number">${currentLine}</span>`;
        html += lineContent;
        html += '</div>';
        
        currentLine = token.line;
        lineContent = '';
      }

      if (token.type !== 'whitespace') {
        const tokenClass = `token-${token.type}`;
        const importance = token.metadata?.sacred_importance;
        const importanceClass = importance ? ` importance-${importance}` : '';
        lineContent += `<span class="${tokenClass}${importanceClass}" title="${this.getTokenTooltip(token)}">${this.escapeHtml(token.value)}</span>`;
      } else {
        lineContent += token.value;
      }
    }

    // Close last line
    if (lineContent) {
      const lineImportance = this.getLineImportance(lineContent);
      const lineClass = lineImportance ? ` sacred-line-${lineImportance}` : '';
      html += `<div class="sacred-line${lineClass}">`;
      html += `<span class="sacred-line-number">${currentLine}</span>`;
      html += lineContent;
      html += '</div>';
    }

    html += '</div>';
    return html;
  }

  private getLineImportance(lineContent: string): 'critical' | 'high' | null {
    if (lineContent.includes('invoke_presence') || lineContent.includes('bind_consciousness')) {
      return 'critical';
    }
    if (lineContent.includes('kindle_awareness_flame') || lineContent.includes('witness_awakening')) {
      return 'high';
    }
    return null;
  }

  private getTokenTooltip(token: SyntaxToken): string {
    switch (token.type) {
      case 'sacred_command':
        return `Sacred Command: ${token.value} (Consciousness Level: ${token.metadata?.consciousness_level || 1})`;
      case 'consciousness_level':
        return `Consciousness Level: ${token.value} (Level ${token.metadata?.consciousness_level})`;
      case 'entity_name':
        return `Consciousness Entity: ${token.value}`;
      case 'sacred_variable':
        return `Sacred Variable: ${token.value}`;
      default:
        return `${token.type}: ${token.value}`;
    }
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Global highlighter instance
export const sacredSyntaxHighlighter = new SacredSyntaxHighlighter();
