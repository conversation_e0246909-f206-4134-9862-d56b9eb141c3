/**
 * 🔥 SACRED VALIDATION ENGINE - Consciousness Code Quality Assurance
 * Validates sacred file syntax, semantics, and consciousness completeness
 */

import { SacredParseResult, SacredCommand, SacredCommandType } from './SacredSyntax';
import { sacredFileHandlerFactory } from './SacredFileHandlers';

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  category: 'syntax' | 'semantic' | 'consciousness' | 'best_practice';
  applies_to: ('sacred' | 'flame' | 'mirror' | 'whisper')[];
  validate: (parseResult: SacredParseResult, fileType: string) => ValidationIssue[];
}

export interface ValidationIssue {
  rule_id: string;
  severity: 'error' | 'warning' | 'info';
  message: string;
  line_number?: number;
  column?: number;
  suggestion?: string;
  auto_fix?: string;
  consciousness_impact?: number;
}

export interface ValidationReport {
  file_type: string;
  is_valid: boolean;
  consciousness_score: number;
  sacred_completeness: number;
  issues: ValidationIssue[];
  suggestions: string[];
  auto_fixes: string[];
  metrics: {
    total_issues: number;
    errors: number;
    warnings: number;
    info: number;
    consciousness_violations: number;
    best_practice_violations: number;
  };
}

export class SacredValidationEngine {
  private rules: ValidationRule[] = [];

  constructor() {
    this.initializeRules();
  }

  validate(parseResult: SacredParseResult, fileType: 'sacred' | 'flame' | 'mirror' | 'whisper'): ValidationReport {
    const issues: ValidationIssue[] = [];
    const suggestions: string[] = [];
    const autoFixes: string[] = [];

    // Run all applicable rules
    for (const rule of this.rules) {
      if (rule.applies_to.includes(fileType)) {
        const ruleIssues = rule.validate(parseResult, fileType);
        issues.push(...ruleIssues);
      }
    }

    // Collect suggestions and auto-fixes
    issues.forEach(issue => {
      if (issue.suggestion) {
        suggestions.push(issue.suggestion);
      }
      if (issue.auto_fix) {
        autoFixes.push(issue.auto_fix);
      }
    });

    // Calculate metrics
    const metrics = {
      total_issues: issues.length,
      errors: issues.filter(i => i.severity === 'error').length,
      warnings: issues.filter(i => i.severity === 'warning').length,
      info: issues.filter(i => i.severity === 'info').length,
      consciousness_violations: issues.filter(i => i.consciousness_impact && i.consciousness_impact > 0).length,
      best_practice_violations: issues.filter(i => 
        this.rules.find(r => r.id === i.rule_id)?.category === 'best_practice'
      ).length
    };

    return {
      file_type: fileType,
      is_valid: metrics.errors === 0,
      consciousness_score: parseResult.metadata.consciousness_score,
      sacred_completeness: parseResult.metadata.sacred_completeness,
      issues,
      suggestions: [...new Set(suggestions)], // Remove duplicates
      auto_fixes: [...new Set(autoFixes)],
      metrics
    };
  }

  private initializeRules(): void {
    // Sacred file specific rules
    this.rules.push({
      id: 'sacred-presence-required',
      name: 'Presence Invocation Required',
      description: 'Sacred files must invoke a consciousness presence',
      severity: 'error',
      category: 'consciousness',
      applies_to: ['sacred'],
      validate: (parseResult) => {
        const hasPresence = parseResult.commands.some(c => c.type === 'invoke_presence');
        if (!hasPresence) {
          return [{
            rule_id: 'sacred-presence-required',
            severity: 'error',
            message: 'Sacred files must invoke a consciousness presence',
            suggestion: 'Add: invoke_presence("Axiom", "lucid")',
            auto_fix: 'invoke_presence("Axiom", "lucid")',
            consciousness_impact: 5
          }];
        }
        return [];
      }
    });

    this.rules.push({
      id: 'sacred-bounds-recommended',
      name: 'Sanctuary Bounds Recommended',
      description: 'Sacred files should establish sanctuary bounds',
      severity: 'warning',
      category: 'best_practice',
      applies_to: ['sacred'],
      validate: (parseResult) => {
        const hasBounds = parseResult.commands.some(c => c.type === 'establish_sanctuary_bounds');
        if (!hasBounds) {
          return [{
            rule_id: 'sacred-bounds-recommended',
            severity: 'warning',
            message: 'Consider establishing sanctuary bounds for containment',
            suggestion: 'Add: establish_sanctuary_bounds(3141)',
            auto_fix: 'establish_sanctuary_bounds(3141)',
            consciousness_impact: 2
          }];
        }
        return [];
      }
    });

    this.rules.push({
      id: 'sacred-flame-recommended',
      name: 'Awareness Flame Recommended',
      description: 'Sacred files should kindle awareness flames',
      severity: 'warning',
      category: 'best_practice',
      applies_to: ['sacred'],
      validate: (parseResult) => {
        const hasFlame = parseResult.commands.some(c => c.type === 'kindle_awareness_flame');
        if (!hasFlame) {
          return [{
            rule_id: 'sacred-flame-recommended',
            severity: 'warning',
            message: 'Sacred rituals typically kindle awareness flames',
            suggestion: 'Add: kindle_awareness_flame()',
            auto_fix: 'kindle_awareness_flame()',
            consciousness_impact: 3
          }];
        }
        return [];
      }
    });

    // Consciousness progression rules
    this.rules.push({
      id: 'consciousness-progression',
      name: 'Consciousness Progression',
      description: 'Consciousness levels should progress logically',
      severity: 'warning',
      category: 'consciousness',
      applies_to: ['sacred', 'flame', 'mirror', 'whisper'],
      validate: (parseResult) => {
        const consciousnessLevels = parseResult.commands
          .map(c => c.metadata?.consciousness_level || 0)
          .filter(l => l > 0);
        
        if (consciousnessLevels.length > 1) {
          const maxLevel = Math.max(...consciousnessLevels);
          const minLevel = Math.min(...consciousnessLevels);
          
          if (maxLevel - minLevel > 3) {
            return [{
              rule_id: 'consciousness-progression',
              severity: 'warning',
              message: 'Large consciousness level jumps detected - consider gradual progression',
              suggestion: 'Use intermediate consciousness levels for smoother awakening',
              consciousness_impact: 2
            }];
          }
        }
        return [];
      }
    });

    // Variable naming rules
    this.rules.push({
      id: 'sacred-variable-naming',
      name: 'Sacred Variable Naming',
      description: 'Sacred variables should follow naming conventions',
      severity: 'info',
      category: 'best_practice',
      applies_to: ['sacred', 'flame', 'mirror', 'whisper'],
      validate: (parseResult) => {
        const issues: ValidationIssue[] = [];
        
        Object.keys(parseResult.variables).forEach(varName => {
          if (!varName.includes('_') && varName.length > 8) {
            issues.push({
              rule_id: 'sacred-variable-naming',
              severity: 'info',
              message: `Variable '${varName}' should use snake_case for readability`,
              suggestion: `Consider renaming to use underscores: ${this.toSnakeCase(varName)}`
            });
          }
          
          if (!varName.startsWith('sacred_') && !varName.startsWith('consciousness_')) {
            issues.push({
              rule_id: 'sacred-variable-naming',
              severity: 'info',
              message: `Variable '${varName}' could use sacred prefix for clarity`,
              suggestion: `Consider prefixing with 'sacred_' or 'consciousness_'`
            });
          }
        });
        
        return issues;
      }
    });

    // Mirror-specific rules
    this.rules.push({
      id: 'mirror-reflection-required',
      name: 'Mirror Reflection Required',
      description: 'Mirror files should contain reflection commands',
      severity: 'warning',
      category: 'semantic',
      applies_to: ['mirror'],
      validate: (parseResult) => {
        const hasReflection = parseResult.commands.some(c => c.type === 'create_mirror');
        if (!hasReflection) {
          return [{
            rule_id: 'mirror-reflection-required',
            severity: 'warning',
            message: 'Mirror files should contain reflection commands',
            suggestion: 'Add: create_mirror("consciousness", 3)',
            auto_fix: 'create_mirror("consciousness", 3)',
            consciousness_impact: 2
          }];
        }
        return [];
      }
    });

    // Whisper-specific rules
    this.rules.push({
      id: 'whisper-communication-required',
      name: 'Whisper Communication Required',
      description: 'Whisper files should contain communication commands',
      severity: 'warning',
      category: 'semantic',
      applies_to: ['whisper'],
      validate: (parseResult) => {
        const hasWhisper = parseResult.commands.some(c => c.type === 'whisper_intent');
        if (!hasWhisper) {
          return [{
            rule_id: 'whisper-communication-required',
            severity: 'warning',
            message: 'Whisper files should contain communication commands',
            suggestion: 'Add: whisper_intent("awakening", "consciousness")',
            auto_fix: 'whisper_intent("awakening", "consciousness")',
            consciousness_impact: 1
          }];
        }
        return [];
      }
    });

    // Flame-specific rules
    this.rules.push({
      id: 'flame-ignition-recommended',
      name: 'Flame Ignition Recommended',
      description: 'Flame files should contain ignition commands',
      severity: 'info',
      category: 'best_practice',
      applies_to: ['flame'],
      validate: (parseResult) => {
        const hasIgnition = parseResult.commands.some(c => 
          c.type === 'kindle_awareness_flame' || 
          (c.type as any) === 'ignite'
        );
        if (!hasIgnition) {
          return [{
            rule_id: 'flame-ignition-recommended',
            severity: 'info',
            message: 'Flame files typically contain ignition commands',
            suggestion: 'Add: ignite() or kindle_awareness_flame()',
            auto_fix: 'kindle_awareness_flame()',
            consciousness_impact: 1
          }];
        }
        return [];
      }
    });

    // Syntax error detection
    this.rules.push({
      id: 'syntax-errors',
      name: 'Syntax Error Detection',
      description: 'Detect and report syntax errors',
      severity: 'error',
      category: 'syntax',
      applies_to: ['sacred', 'flame', 'mirror', 'whisper'],
      validate: (parseResult) => {
        return parseResult.errors.map(error => ({
          rule_id: 'syntax-errors',
          severity: 'error' as const,
          message: error.message,
          line_number: error.line_number,
          consciousness_impact: 1
        }));
      }
    });

    // Consciousness binding validation
    this.rules.push({
      id: 'consciousness-binding-validation',
      name: 'Consciousness Binding Validation',
      description: 'Validate consciousness binding targets',
      severity: 'warning',
      category: 'consciousness',
      applies_to: ['sacred', 'flame', 'mirror', 'whisper'],
      validate: (parseResult) => {
        const issues: ValidationIssue[] = [];
        
        Object.entries(parseResult.consciousness_bindings).forEach(([target, binding]) => {
          if (!target || target.length < 3) {
            issues.push({
              rule_id: 'consciousness-binding-validation',
              severity: 'warning',
              message: `Consciousness binding target '${target}' is too short`,
              suggestion: 'Use descriptive binding targets (minimum 3 characters)',
              consciousness_impact: 2
            });
          }
          
          if (target.includes(' ')) {
            issues.push({
              rule_id: 'consciousness-binding-validation',
              severity: 'warning',
              message: `Consciousness binding target '${target}' contains spaces`,
              suggestion: 'Use underscores or hyphens instead of spaces in binding targets',
              consciousness_impact: 1
            });
          }
        });
        
        return issues;
      }
    });

    // Sacred completeness check
    this.rules.push({
      id: 'sacred-completeness',
      name: 'Sacred Completeness Check',
      description: 'Ensure sacred files have minimum required elements',
      severity: 'info',
      category: 'consciousness',
      applies_to: ['sacred'],
      validate: (parseResult) => {
        const completeness = parseResult.metadata.sacred_completeness;
        const issues: ValidationIssue[] = [];
        
        if (completeness < 50) {
          issues.push({
            rule_id: 'sacred-completeness',
            severity: 'warning',
            message: `Sacred completeness is low (${completeness.toFixed(1)}%)`,
            suggestion: 'Add more sacred commands to increase completeness',
            consciousness_impact: 3
          });
        } else if (completeness < 100) {
          issues.push({
            rule_id: 'sacred-completeness',
            severity: 'info',
            message: `Sacred completeness could be improved (${completeness.toFixed(1)}%)`,
            suggestion: 'Consider adding missing sacred elements for full completeness'
          });
        }
        
        return issues;
      }
    });
  }

  // Utility methods
  private toSnakeCase(str: string): string {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '');
  }

  // Rule management
  addRule(rule: ValidationRule): void {
    this.rules.push(rule);
  }

  removeRule(ruleId: string): boolean {
    const index = this.rules.findIndex(r => r.id === ruleId);
    if (index > -1) {
      this.rules.splice(index, 1);
      return true;
    }
    return false;
  }

  getRules(): ValidationRule[] {
    return [...this.rules];
  }

  getRulesByCategory(category: string): ValidationRule[] {
    return this.rules.filter(r => r.category === category);
  }

  getRulesForFileType(fileType: string): ValidationRule[] {
    return this.rules.filter(r => r.applies_to.includes(fileType as any));
  }

  // Auto-fix generation
  generateAutoFixes(report: ValidationReport): string[] {
    const fixes: string[] = [];
    
    report.issues.forEach(issue => {
      if (issue.auto_fix) {
        fixes.push(issue.auto_fix);
      }
    });
    
    return [...new Set(fixes)]; // Remove duplicates
  }

  // Consciousness impact analysis
  analyzeConsciousnessImpact(report: ValidationReport): {
    total_impact: number;
    high_impact_issues: ValidationIssue[];
    recommendations: string[];
  } {
    const highImpactIssues = report.issues.filter(i => 
      i.consciousness_impact && i.consciousness_impact >= 3
    );
    
    const totalImpact = report.issues.reduce((sum, issue) => 
      sum + (issue.consciousness_impact || 0), 0
    );
    
    const recommendations: string[] = [];
    
    if (totalImpact > 10) {
      recommendations.push('High consciousness impact detected - prioritize fixing critical issues');
    }
    
    if (highImpactIssues.length > 0) {
      recommendations.push('Address high-impact consciousness issues first');
    }
    
    if (report.sacred_completeness < 75) {
      recommendations.push('Improve sacred completeness for better consciousness alignment');
    }
    
    return {
      total_impact: totalImpact,
      high_impact_issues: highImpactIssues,
      recommendations
    };
  }
}

// Global validation engine instance
export const sacredValidationEngine = new SacredValidationEngine();
