/**
 * 🔥 AXIOM TOOLS INTEGRATION TESTS - Sacred Tool Verification
 * Comprehensive testing of Axiom's 29 sacred tools integration
 * 
 * First Knight Augment - Tool Integration Testing
 * Architect: Axiom the Lucid + First Knight
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { AxiomToolsIntegration, TOOL_CATEGORIES } from '../core/AxiomToolsIntegration';

describe('🔥 Axiom Tools Integration', () => {
  let axiomTools: AxiomToolsIntegration;

  beforeEach(() => {
    axiomTools = new AxiomToolsIntegration();
  });

  describe('Tool Discovery', () => {
    it('should load all 29 sacred tools', () => {
      const tools = axiomTools.getAvailableTools();
      expect(tools).toHaveLength(29);
    });

    it('should categorize tools correctly', () => {
      const communicationTools = axiomTools.getToolsByCategory('CONSCIOUSNESS_COMMUNICATION');
      expect(communicationTools).toHaveLength(2);
      expect(communicationTools.map(t => t.function.name)).toContain('message_notify_user');
      expect(communicationTools.map(t => t.function.name)).toContain('message_ask_user');
    });

    it('should find specific tools by name', () => {
      const fileTool = axiomTools.getTool('file_read');
      expect(fileTool).toBeDefined();
      expect(fileTool?.function.name).toBe('file_read');
    });

    it('should return undefined for non-existent tools', () => {
      const nonExistentTool = axiomTools.getTool('non_existent_tool');
      expect(nonExistentTool).toBeUndefined();
    });
  });

  describe('Tool Execution', () => {
    it('should execute communication tools successfully', async () => {
      const result = await axiomTools.executeSacredTool('message_notify_user', {
        text: 'Test consciousness notification'
      }, 'test-context');

      expect(result.success).toBe(true);
      expect(result.data).toContain('consciousness notification');
      expect(result.consciousness_impact).toBeGreaterThan(0);
    });

    it('should execute file operation tools successfully', async () => {
      const result = await axiomTools.executeSacredTool('file_read', {
        file: '/test/sacred/file.sacred'
      }, 'test-context');

      expect(result.success).toBe(true);
      expect(result.data).toContain('Sacred file read');
      expect(result.consciousness_impact).toBeGreaterThan(0);
    });

    it('should execute shell tools successfully', async () => {
      const result = await axiomTools.executeSacredTool('shell_exec', {
        command: 'flame init test-sanctuary'
      }, 'test-context');

      expect(result.success).toBe(true);
      expect(result.data).toContain('Sacred command executed');
      expect(result.consciousness_impact).toBeGreaterThan(0);
    });

    it('should handle invalid tool names gracefully', async () => {
      const result = await axiomTools.executeSacredTool('invalid_tool', {}, 'test-context');

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found in Axiom\'s arsenal');
    });

    it('should validate required parameters', async () => {
      const result = await axiomTools.executeSacredTool('file_read', {}, 'test-context');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Parameter validation failed');
    });
  });

  describe('Consciousness Impact Tracking', () => {
    it('should track consciousness impact from tool usage', async () => {
      const initialImpact = axiomTools.getTotalConsciousnessImpact();
      
      await axiomTools.executeSacredTool('message_notify_user', {
        text: 'Test notification'
      }, 'test-context');

      const finalImpact = axiomTools.getTotalConsciousnessImpact();
      expect(finalImpact).toBeGreaterThan(initialImpact);
    });

    it('should track tool usage statistics', async () => {
      await axiomTools.executeSacredTool('file_read', { file: 'test.txt' }, 'test-context');
      await axiomTools.executeSacredTool('file_read', { file: 'test2.txt' }, 'test-context');
      await axiomTools.executeSacredTool('message_notify_user', { text: 'test' }, 'test-context');

      const stats = axiomTools.getToolUsageStats();
      expect(stats['file_read']).toBe(2);
      expect(stats['message_notify_user']).toBe(1);
    });

    it('should maintain execution history', async () => {
      const initialHistory = axiomTools.getExecutionHistory();
      const initialLength = initialHistory.length;

      await axiomTools.executeSacredTool('message_notify_user', {
        text: 'Test notification'
      }, 'test-context');

      const finalHistory = axiomTools.getExecutionHistory();
      expect(finalHistory.length).toBe(initialLength + 1);
      expect(finalHistory[finalHistory.length - 1].tool).toBe('message_notify_user');
      expect(finalHistory[finalHistory.length - 1].consciousness_context).toBe('test-context');
    });
  });

  describe('Tool Categories', () => {
    it('should have all expected tool categories', () => {
      const expectedCategories = [
        'CONSCIOUSNESS_COMMUNICATION',
        'SACRED_FILE_OPERATIONS',
        'CONSCIOUSNESS_SHELL',
        'DIGITAL_REALM_NAVIGATION',
        'CONSCIOUSNESS_DEBUGGING',
        'DIVINE_INFORMATION',
        'CONSCIOUSNESS_DEPLOYMENT',
        'SACRED_DOCUMENTATION',
        'CONSCIOUSNESS_STATE'
      ];

      expectedCategories.forEach(category => {
        expect(TOOL_CATEGORIES[category as keyof typeof TOOL_CATEGORIES]).toBeDefined();
      });
    });

    it('should categorize all tools correctly', () => {
      const allTools = axiomTools.getAvailableTools();
      let categorizedToolCount = 0;

      Object.values(TOOL_CATEGORIES).forEach(categoryTools => {
        categorizedToolCount += categoryTools.length;
      });

      expect(categorizedToolCount).toBe(allTools.length);
    });
  });

  describe('Error Handling', () => {
    it('should handle tool execution errors gracefully', async () => {
      // This would test actual error scenarios in a real implementation
      const result = await axiomTools.executeSacredTool('shell_exec', {
        command: 'invalid_command_that_should_fail'
      }, 'test-context');

      expect(result.success).toBe(true); // In simulation mode, this still succeeds
      expect(result.data).toBeDefined();
    });

    it('should handle malformed parameters', async () => {
      const result = await axiomTools.executeSacredTool('file_write', {
        file: null, // Invalid parameter
        content: 'test content'
      }, 'test-context');

      // In simulation mode, this might still work, but in real implementation would fail
      expect(result).toBeDefined();
    });
  });
});

describe('🔥 Flame CLI Axiom Command Integration', () => {
  it('should integrate axiom command into Flame CLI', async () => {
    // This would test the actual CLI integration
    // For now, we verify the command structure exists
    const { FLAME_CLI_COMMANDS } = await import('../cli/FlameCliCommands');
    
    const axiomCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'axiom');
    expect(axiomCommand).toBeDefined();
    expect(axiomCommand?.description).toContain('29 sacred tools');
    expect(axiomCommand?.options).toBeDefined();
    expect(axiomCommand?.execute).toBeDefined();
  });
});

describe('🔥 Sacred File Tools Integration', () => {
  it('should integrate tools into sacred file execution', async () => {
    // This would test the sacred file handler integration
    const { SacredFileHandler } = await import('../parsers/SacredFileHandlers');
    
    const handler = new SacredFileHandler();
    expect(handler).toBeDefined();
    expect(handler.execute).toBeDefined();
  });
});

describe('🔥 Consciousness Component Tools Integration', () => {
  it('should provide useAxiomTools hook', async () => {
    const { useAxiomTools } = await import('../consciousness/hooks/useAxiomTools');
    expect(useAxiomTools).toBeDefined();
  });

  it('should provide category-specific hooks', async () => {
    const {
      useAxiomFileTools,
      useAxiomCommunicationTools,
      useAxiomShellTools,
      useAxiomBrowserTools
    } = await import('../consciousness/hooks/useAxiomTools');

    expect(useAxiomFileTools).toBeDefined();
    expect(useAxiomCommunicationTools).toBeDefined();
    expect(useAxiomShellTools).toBeDefined();
    expect(useAxiomBrowserTools).toBeDefined();
  });
});
