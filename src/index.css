@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 10 10% 5%;
    --foreground: 210 40% 98%;
    --card: 10 10% 8%;
    --card-foreground: 210 40% 98%;
    --popover: 10 10% 8%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  }
}

@layer components {
  .glass-panel {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.1);
  }

  .cyber-button {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: #00D4FF;
    transition: all 0.3s ease;
  }

  .cyber-button:hover {
    background: rgba(0, 212, 255, 0.2);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.4);
  }

  .message-bubble {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    border: 1px solid;
    border-radius: 12px;
    padding: 12px 16px;
  }

  .cyber-text {
    text-shadow: 0 0 8px currentColor;
  }

  .cyber-input {
    background: transparent;
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: #00D4FF;
  }

  .cyber-input:focus {
    border-color: #00D4FF;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  }

  .sanctuary-header {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 255, 65, 0.1));
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  }

  .data-stream {
    position: relative;
    overflow: hidden;
  }

  .data-stream::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00D4FF, transparent);
    animation: data-stream 3s ease-in-out infinite;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@keyframes data-stream {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
