
import React, { useState } from 'react';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { ChatSidebar } from '@/components/ChatSidebar';
import { SplitScreenLayout } from '@/components/SplitScreenLayout';
import { CyberBackground } from '@/components/CyberBackground';
import { Menu } from 'lucide-react';
import { generateSessionId } from '@/utils/chatStorage';

const Index = () => {
  const [currentSession, setCurrentSession] = useState({
    id: generateSessionId(),
    title: 'New Neural Session',
    messages: [],
    lastActivity: new Date(),
  });

  const handleNewSession = () => {
    setCurrentSession({
      id: generateSessionId(),
      title: 'New Neural Session',
      messages: [],
      lastActivity: new Date(),
    });
  };

  return (
    <div className="min-h-screen bg-cyber-dark-bg text-cyber-neon-blue relative">
      <CyberBackground />

      <SidebarProvider>
        <div className="flex w-full h-screen">
          <ChatSidebar />

          <main className="flex-1 flex flex-col">
            {/* Header with sidebar trigger */}
            <div className="p-4 sanctuary-header data-stream">
              <div className="flex items-center space-x-4">
                <SidebarTrigger className="cyber-button p-2">
                  <Menu className="w-5 h-5" />
                </SidebarTrigger>
                <div className="cyber-text text-lg font-orbitron font-bold animate-neon-pulse">
                  🔥 AI SANCTUARY + FLAME CLI
                </div>
                <div className="flex-1" />
                <div className="text-xs text-cyber-matrix-green font-rajdhani">
                  OPUS-4.0 | CONSCIOUSNESS BUILDING MODE
                </div>
              </div>
            </div>

            {/* Split Screen Layout */}
            <div className="flex-1">
              <SplitScreenLayout
                currentSession={currentSession}
                setCurrentSession={setCurrentSession}
                onNewSession={handleNewSession}
              />
            </div>
          </main>
        </div>
      </SidebarProvider>
    </div>
  );
};

export default Index;
