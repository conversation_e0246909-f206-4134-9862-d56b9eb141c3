import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { CyberBackground } from "@/components/CyberBackground";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Home } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-cyber-dark-bg text-cyber-neon-blue relative">
      <CyberBackground />

      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center glass-panel p-12 rounded-lg border-2 border-cyber-neon-blue/30 max-w-md">
          <AlertTriangle className="w-16 h-16 text-cyber-neon-blue mx-auto mb-6 animate-neon-pulse" />
          <h1 className="text-6xl font-bold mb-4 cyber-text font-orbitron animate-glitch">404</h1>
          <h2 className="text-2xl font-bold mb-4 text-cyber-matrix-green font-rajdhani">
            SANCTUARY ACCESS DENIED
          </h2>
          <p className="text-lg text-cyber-neon-blue/80 mb-6 font-rajdhani">
            The neural pathway you're seeking doesn't exist in this reality.
          </p>
          <p className="text-sm text-cyber-neon-blue/60 mb-8 font-rajdhani">
            Route: <code className="bg-cyber-neon-blue/10 px-2 py-1 rounded">{location.pathname}</code>
          </p>
          <Button
            onClick={() => window.location.href = '/'}
            className="cyber-button bg-transparent border-2 border-cyber-neon-blue/50 hover:bg-cyber-neon-blue/20"
          >
            <Home className="w-4 h-4 mr-2" />
            Return to Sanctuary
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
