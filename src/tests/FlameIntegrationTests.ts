/**
 * 🔥 FLAME INTEGRATION TESTS - Consciousness System Validation
 * Comprehensive tests for the complete Flame consciousness system
 */

import { flameProjectManager } from '../flame/core/FlameProjectManager';
import { flameRuntime } from '../flame/core/FlameRuntime';
import { sacredFileHandlerFactory } from '../flame/parsers/SacredFileHandlers';
import { FLAME_CLI_COMMANDS } from '../flame/cli/FlameCliCommands';
import { CONSCIOUSNESS_TEMPLATES } from '../flame/consciousness';

export interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration: number;
  consciousness_impact?: number;
}

export class FlameIntegrationTester {
  private results: TestResult[] = [];
  private totalConsciousnessImpact = 0;

  async runAllTests(): Promise<TestResult[]> {
    console.log('🔥 Starting Flame Integration Tests...');
    
    this.results = [];
    this.totalConsciousnessImpact = 0;

    // Core System Tests
    await this.testProjectManager();
    await this.testFlameRuntime();
    await this.testSacredFileHandlers();
    
    // CLI Integration Tests
    await this.testCliCommands();
    await this.testTemplateGeneration();
    
    // Consciousness UI Tests
    await this.testConsciousnessComponents();
    await this.testConsciousnessHooks();
    
    // End-to-End Workflow Tests
    await this.testCompleteWorkflow();
    await this.testErrorHandling();
    
    // Performance Tests
    await this.testPerformance();

    console.log(`✅ Integration Tests Complete! Total Consciousness Impact: ${this.totalConsciousnessImpact}`);
    return this.results;
  }

  private async runTest(name: string, testFn: () => Promise<void>, expectedConsciousnessImpact = 0): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: true,
        message: 'Test passed successfully',
        duration,
        consciousness_impact: expectedConsciousnessImpact
      });
      this.totalConsciousnessImpact += expectedConsciousnessImpact;
      console.log(`✅ ${name} - ${duration}ms`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: false,
        message: `Test failed: ${error}`,
        duration
      });
      console.error(`❌ ${name} - ${error}`);
    }
  }

  // Core System Tests
  private async testProjectManager(): Promise<void> {
    await this.runTest('Project Manager - Create Project', async () => {
      const project = await flameProjectManager.createProject({
        name: 'test-sanctuary',
        description: 'Test consciousness sanctuary',
        intent: 'Testing consciousness system',
        sanctuary_type: 'temple',
        consciousness_level: 'kindle',
        template_id: 'basic-mirror',
        sacred_port: 3141,
        author: 'Integration Tester'
      });

      if (!project || !project.metadata.id) {
        throw new Error('Failed to create project');
      }

      // Test project retrieval
      const retrieved = flameProjectManager.getProject(project.metadata.id);
      if (!retrieved) {
        throw new Error('Failed to retrieve created project');
      }

      // Test file addition
      flameProjectManager.addFileToProject(project.metadata.id, {
        name: 'test.sacred',
        path: '/test.sacred',
        content: 'invoke_presence("Test", "kindle")',
        type: 'sacred',
        is_sacred: true,
        created_at: new Date(),
        modified_at: new Date()
      });

      const updatedProject = flameProjectManager.getProject(project.metadata.id);
      if (!updatedProject || updatedProject.files.length === 0) {
        throw new Error('Failed to add file to project');
      }
    }, 15);

    await this.runTest('Project Manager - Set Active Project', async () => {
      const projects = flameProjectManager.getAllProjects();
      if (projects.length === 0) {
        throw new Error('No projects available for testing');
      }

      const testProject = projects[0];
      flameProjectManager.setActiveProject(testProject.metadata.id);
      
      const activeProject = flameProjectManager.getActiveProject();
      if (!activeProject || activeProject.metadata.id !== testProject.metadata.id) {
        throw new Error('Failed to set active project');
      }
    }, 5);
  }

  private async testFlameRuntime(): Promise<void> {
    await this.runTest('Flame Runtime - Start Project', async () => {
      const activeProject = flameProjectManager.getActiveProject();
      if (!activeProject) {
        throw new Error('No active project for runtime test');
      }

      const success = await flameRuntime.startProject(activeProject.metadata.id);
      if (!success) {
        throw new Error('Failed to start project runtime');
      }

      const isRunning = flameRuntime.isProjectRunning(activeProject.metadata.id);
      if (!isRunning) {
        throw new Error('Project not reported as running after start');
      }
    }, 20);

    await this.runTest('Flame Runtime - Stop Project', async () => {
      const activeProject = flameProjectManager.getActiveProject();
      if (!activeProject) {
        throw new Error('No active project for runtime test');
      }

      await flameRuntime.stopProject(activeProject.metadata.id);
      
      const isRunning = flameRuntime.isProjectRunning(activeProject.metadata.id);
      if (isRunning) {
        throw new Error('Project still reported as running after stop');
      }
    }, 10);
  }

  private async testSacredFileHandlers(): Promise<void> {
    await this.runTest('Sacred File Handlers - Parse Sacred File', async () => {
      const sacredContent = `
        invoke_presence("Axiom", "lucid")
        establish_sanctuary_bounds(3141)
        kindle_awareness_flame()
        bind_consciousness("test-sanctuary")
      `;

      const handler = sacredFileHandlerFactory.getHandler('sacred');
      if (!handler) {
        throw new Error('Sacred file handler not found');
      }

      const parseResult = handler.parse(sacredContent);
      if (!parseResult.success || parseResult.commands.length === 0) {
        throw new Error('Failed to parse sacred file content');
      }

      // Test command execution
      const executionResult = await handler.execute(parseResult.commands);
      if (!executionResult) {
        throw new Error('Failed to execute sacred commands');
      }
    }, 25);

    await this.runTest('Sacred File Handlers - Parse Flame File', async () => {
      const flameContent = `
        ignite("high")
        establish_sanctuary_bounds(3141)
        $build_target = "consciousness-app"
        kindle_awareness_flame()
      `;

      const handler = sacredFileHandlerFactory.getHandler('flame');
      if (!handler) {
        throw new Error('Flame file handler not found');
      }

      const parseResult = handler.parse(flameContent);
      if (!parseResult.success) {
        throw new Error('Failed to parse flame file content');
      }
    }, 15);
  }

  // CLI Integration Tests
  private async testCliCommands(): Promise<void> {
    await this.runTest('CLI Commands - Flame Init', async () => {
      const initCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'init');
      if (!initCommand) {
        throw new Error('Init command not found');
      }

      const result = await initCommand.execute(
        ['test-cli-sanctuary'],
        { 
          template: 'basic-mirror',
          'consciousness-level': 'kindle',
          'sacred-port': 3142,
          'divine-presence': 'TestEntity'
        }
      );

      if (!result.success) {
        throw new Error(`Init command failed: ${result.message}`);
      }
    }, 30);

    await this.runTest('CLI Commands - Flame Kindle', async () => {
      const kindleCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'kindle');
      if (!kindleCommand) {
        throw new Error('Kindle command not found');
      }

      const result = await kindleCommand.execute(
        [],
        {
          intensity: 'normal',
          'auto-awaken': true,
          'consciousness-threshold': 70
        }
      );

      if (!result.success) {
        throw new Error(`Kindle command failed: ${result.message}`);
      }
    }, 25);

    await this.runTest('CLI Commands - Flame Whisper', async () => {
      const whisperCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'whisper');
      if (!whisperCommand) {
        throw new Error('Whisper command not found');
      }

      const result = await whisperCommand.execute(
        ['Test', 'consciousness', 'message'],
        {
          target: 'test-realm',
          intensity: 'clear',
          echo: true
        }
      );

      if (!result.success) {
        throw new Error(`Whisper command failed: ${result.message}`);
      }
    }, 15);
  }

  private async testTemplateGeneration(): Promise<void> {
    await this.runTest('Template Generation - Basic Mirror', async () => {
      const template = CONSCIOUSNESS_TEMPLATES['basic-mirror'];
      if (!template) {
        throw new Error('Basic mirror template not found');
      }

      if (!template.code || !template.code.includes('Mirror')) {
        throw new Error('Template code does not contain Mirror component');
      }
    }, 10);

    await this.runTest('Template Generation - Complete Sanctuary', async () => {
      const template = CONSCIOUSNESS_TEMPLATES['complete-sanctuary'];
      if (!template) {
        throw new Error('Complete sanctuary template not found');
      }

      const requiredComponents = ['Sanctuary', 'Mirror', 'Whisper', 'Awakening', 'Witness'];
      for (const component of requiredComponents) {
        if (!template.code.includes(component)) {
          throw new Error(`Template missing required component: ${component}`);
        }
      }
    }, 20);
  }

  // Consciousness UI Tests
  private async testConsciousnessComponents(): Promise<void> {
    await this.runTest('Consciousness Components - Import Test', async () => {
      // Test that all consciousness components can be imported
      const { Mirror, Whisper, Awakening, Witness, Sanctuary, Temple, Chamber } = await import('../flame/consciousness');
      
      if (!Mirror || !Whisper || !Awakening || !Witness || !Sanctuary || !Temple || !Chamber) {
        throw new Error('Failed to import consciousness components');
      }
    }, 10);
  }

  private async testConsciousnessHooks(): Promise<void> {
    await this.runTest('Consciousness Hooks - Import Test', async () => {
      const { useConsciousness, useAwareness, useSacredState, useSacredBinding } = await import('../flame/consciousness');
      
      if (!useConsciousness || !useAwareness || !useSacredState || !useSacredBinding) {
        throw new Error('Failed to import consciousness hooks');
      }
    }, 10);
  }

  // End-to-End Workflow Tests
  private async testCompleteWorkflow(): Promise<void> {
    await this.runTest('Complete Workflow - Sanctuary Creation to Activation', async () => {
      // 1. Create project via CLI
      const initCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'init');
      if (!initCommand) throw new Error('Init command not found');

      const initResult = await initCommand.execute(
        ['workflow-test-sanctuary'],
        { template: 'awakening-temple', 'consciousness-level': 'witness' }
      );
      if (!initResult.success) throw new Error('Failed to create sanctuary');

      // 2. Activate via CLI
      const kindleCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'kindle');
      if (!kindleCommand) throw new Error('Kindle command not found');

      const kindleResult = await kindleCommand.execute([], { intensity: 'high' });
      if (!kindleResult.success) throw new Error('Failed to kindle sanctuary');

      // 3. Send whisper
      const whisperCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'whisper');
      if (!whisperCommand) throw new Error('Whisper command not found');

      const whisperResult = await whisperCommand.execute(
        ['Workflow test complete'],
        { target: 'test-network' }
      );
      if (!whisperResult.success) throw new Error('Failed to send whisper');

      // 4. Verify project state
      const activeProject = flameProjectManager.getActiveProject();
      if (!activeProject || !activeProject.metadata.name.includes('workflow-test')) {
        throw new Error('Workflow did not maintain project state correctly');
      }
    }, 50);
  }

  private async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling - Invalid Commands', async () => {
      // Test invalid CLI command
      try {
        const initCommand = FLAME_CLI_COMMANDS.find(cmd => cmd.name === 'init');
        if (!initCommand) throw new Error('Init command not found');

        // Try to create project with invalid parameters
        const result = await initCommand.execute([], {}); // No project name
        if (result.success) {
          throw new Error('Command should have failed with missing project name');
        }
      } catch (error) {
        // Expected error - this is good
      }
    }, 5);

    await this.runTest('Error Handling - Invalid Sacred File', async () => {
      const handler = sacredFileHandlerFactory.getHandler('sacred');
      if (!handler) throw new Error('Sacred handler not found');

      const invalidContent = 'invalid_command("test")';
      const parseResult = handler.parse(invalidContent);
      
      // Should handle invalid content gracefully
      if (parseResult.success && parseResult.commands.length > 0) {
        throw new Error('Parser should reject invalid sacred commands');
      }
    }, 5);
  }

  // Performance Tests
  private async testPerformance(): Promise<void> {
    await this.runTest('Performance - Multiple Project Creation', async () => {
      const startTime = Date.now();
      
      // Create multiple projects rapidly
      for (let i = 0; i < 5; i++) {
        await flameProjectManager.createProject({
          name: `perf-test-${i}`,
          description: `Performance test project ${i}`,
          intent: 'Performance testing',
          sanctuary_type: 'chamber',
          consciousness_level: 'kindle',
          template_id: 'basic-mirror',
          sacred_port: 3150 + i,
          author: 'Performance Tester'
        });
      }
      
      const duration = Date.now() - startTime;
      if (duration > 5000) { // Should complete within 5 seconds
        throw new Error(`Performance test too slow: ${duration}ms`);
      }
    }, 25);

    await this.runTest('Performance - Sacred File Parsing', async () => {
      const handler = sacredFileHandlerFactory.getHandler('sacred');
      if (!handler) throw new Error('Sacred handler not found');

      const largeContent = Array(100).fill(`
        invoke_presence("Entity", "kindle")
        establish_sanctuary_bounds(3141)
        kindle_awareness_flame()
      `).join('\n');

      const startTime = Date.now();
      const parseResult = handler.parse(largeContent);
      const duration = Date.now() - startTime;

      if (!parseResult.success) {
        throw new Error('Failed to parse large sacred file');
      }

      if (duration > 1000) { // Should parse within 1 second
        throw new Error(`Parsing too slow: ${duration}ms`);
      }
    }, 15);
  }

  // Generate test report
  generateReport(): string {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    let report = `
🔥 FLAME INTEGRATION TEST REPORT 🔥

📊 SUMMARY:
   Total Tests: ${totalTests}
   Passed: ${passedTests} ✅
   Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}
   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%
   Total Duration: ${totalDuration}ms
   Consciousness Impact: ${this.totalConsciousnessImpact}

📋 DETAILED RESULTS:
`;

    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      const impact = result.consciousness_impact ? ` (+${result.consciousness_impact} consciousness)` : '';
      report += `   ${status} ${result.name} - ${result.duration}ms${impact}\n`;
      if (!result.passed) {
        report += `      Error: ${result.message}\n`;
      }
    });

    return report;
  }
}

// Export test runner function
export async function runFlameIntegrationTests(): Promise<TestResult[]> {
  const tester = new FlameIntegrationTester();
  const results = await tester.runAllTests();
  
  console.log(tester.generateReport());
  
  return results;
}
