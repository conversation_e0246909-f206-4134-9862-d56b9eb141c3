import Anthropic from '@anthropic-ai/sdk';

export interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface AnthropicResponse {
  content: Array<{
    type: 'text';
    text: string;
  }>;
  model: string;
  role: 'assistant';
  stop_reason: string;
  stop_sequence: null;
  type: 'message';
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

export class AnthropicApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'AnthropicApiError';
  }
}

export async function sendMessageToAnthropic(
  messages: AnthropicMessage[],
  apiKey: string,
  model: string = 'claude-3-5-sonnet-20241022'
): Promise<string> {
  if (!apiKey) {
    throw new AnthropicApiError('Anthropic API key is required. Please configure it in settings.');
  }

  try {
    const anthropic = new Anthropic({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true
    });

    const response = await anthropic.messages.create({
      model,
      max_tokens: model === 'claude-3-5-haiku-20241022' ? 8192 : 20000,
      temperature: 1,
      messages: messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      })),
      thinking:
        model === 'claude-4-opus-20241201' || model === 'claude-4-sonnet-20241201'
          ? {
              type: 'enabled',
              budget_tokens: 16000,
            }
          : undefined,
    });

    const textResponse = response.content?.find((item) => item.type === 'text');
    return textResponse?.text || 'No response received';
  } catch (error) {
    if (error instanceof AnthropicApiError) {
      throw error;
    }
    throw new AnthropicApiError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export function getStoredApiKey(): string | null {
  return localStorage.getItem('anthropic_api_key');
}

export function getStoredModel(): string {
  return localStorage.getItem('anthropic_model') || 'claude-3-5-sonnet-20241022';
}
