
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				'orbitron': ['Orbitron', 'monospace'],
				'rajdhani': ['Rajdhani', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// AI Sanctuary cyberpunk color palette
				cyber: {
					'neon-blue': '#00D4FF',
					'neon-cyan': '#00FFFF',
					'matrix-green': '#00FF41',
					'hot-pink': '#FF1493',
					'electric-purple': '#8A2BE2',
					'neon-purple': '#8A2BE2',
					'neon-orange': '#FF6B35',
					'neon-red': '#FF4444',
					'neon-yellow': '#FFFF00',
					'neon-pink': '#FF1493',
					'warm-orange': '#FF8C42',
					'soft-red': '#E85A4F',
					'amber': '#FFB347',
					'coral': '#FF7F7F',
					'dark-bg': '#0a0a0a',
					'glass-bg': 'rgba(0, 0, 0, 0.7)',
					'glass-border': 'rgba(0, 212, 255, 0.3)',
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'neon-pulse': {
					'0%, 100%': {
						textShadow: '0 0 2px #00D4FF, 0 0 4px #00D4FF, 0 0 6px #00D4FF'
					},
					'50%': {
						textShadow: '0 0 1px #00D4FF, 0 0 3px #00D4FF, 0 0 8px #00D4FF, 0 0 12px #00D4FF'
					}
				},
				'cyber-glow': {
					'0%, 100%': {
						boxShadow: '0 0 3px rgba(0, 212, 255, 0.3), 0 0 6px rgba(0, 212, 255, 0.2), inset 0 0 5px rgba(0, 212, 255, 0.1)'
					},
					'50%': {
						boxShadow: '0 0 5px rgba(0, 212, 255, 0.4), 0 0 10px rgba(0, 212, 255, 0.3), inset 0 0 8px rgba(0, 212, 255, 0.15)'
					}
				},
				'matrix-rain': {
					'0%': { transform: 'translateY(-100%)' },
					'100%': { transform: 'translateY(100vh)' }
				},
				'glitch': {
					'0%, 100%': { transform: 'translate(0)' },
					'20%': { transform: 'translate(-2px, 2px)' },
					'40%': { transform: 'translate(-2px, -2px)' },
					'60%': { transform: 'translate(2px, 2px)' },
					'80%': { transform: 'translate(2px, -2px)' }
				},
				'data-stream': {
					'0%': { opacity: 0, transform: 'translateX(-100%)' },
					'50%': { opacity: 1 },
					'100%': { opacity: 0, transform: 'translateX(100%)' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'neon-pulse': 'neon-pulse 2s ease-in-out infinite',
				'cyber-glow': 'cyber-glow 3s ease-in-out infinite',
				'matrix-rain': 'matrix-rain 3s linear infinite',
				'glitch': 'glitch 0.3s ease-in-out infinite',
				'data-stream': 'data-stream 2s ease-in-out infinite'
			},
			backdropBlur: {
				'xs': '2px',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
